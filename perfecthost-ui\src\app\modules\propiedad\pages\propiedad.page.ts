import { Component } from '@angular/core';
import { HospedajeComponent } from '../components/hospedaje/hospedaje.component';
import { ContentCardComponent } from '../../../shared/components/content-card/content-card.component';
import { PageHeaderComponent } from '../../../shared/components/page-header/page-header.component';

@Component({
  selector: 'app-propiedad-page',
  standalone: true,
  imports: [HospedajeComponent, ContentCardComponent, PageHeaderComponent],
  template: `
    <div class="bg-background min-h-screen">
      <div class="container mx-auto px-6 py-8">
        <!-- Header Section -->
        <app-page-header
          title="Gestión de Propiedades"
          description="Administra y gestiona todas tus propiedades desde un solo lugar">
        </app-page-header>

        <!-- Action Bar -->
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div class="flex items-center space-x-4">
            <button class="bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded-lg font-medium transition-colors">
              Nueva Propiedad
            </button>
            <button class="border border-border text-foreground hover:bg-card px-4 py-2 rounded-lg font-medium transition-colors">
              Importar
            </button>
          </div>
          <div class="flex items-center space-x-2">
            <span class="text-sm text-muted-foreground">Vista:</span>
            <button class="p-2 rounded-md hover:bg-card transition-colors">
              <svg class="w-4 h-4 text-muted-foreground" fill="currentColor" viewBox="0 0 20 20">
                <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
              </svg>
            </button>
          </div>
        </div>

        <!-- Content Card -->
        <app-content-card title="Mis Propiedades">
          <div slot="actions">
            <span class="text-sm text-muted-foreground">Total: 0 propiedades</span>
          </div>
          <app-hospedaje></app-hospedaje>
        </app-content-card>
      </div>
    </div>
  `,
  styles: [`
    .container {
      max-width: 1400px;
    }
  `]
})
export class PropiedadPage {}
