import { Component, OnInit } from '@angular/core';
import { RouterLink } from '@angular/router';
import { AngularSvgIconModule } from 'angular-svg-icon';

@Component({
  selector: 'app-perfecthost-navbar',
  templateUrl: './perfecthost-navbar.component.html',
  styleUrls: ['./perfecthost-navbar.component.css'],
  standalone: true,
  imports: [
    RouterLink,
    AngularSvgIconModule
  ],
})
export class PerfecthostNavbarComponent implements OnInit {
  public showMobileMenu = false;

  constructor() {}

  ngOnInit(): void {}

  public toggleMobileMenu(): void {
    this.showMobileMenu = !this.showMobileMenu;
  }

  public closeMobileMenu(): void {
    this.showMobileMenu = false;
  }
}
