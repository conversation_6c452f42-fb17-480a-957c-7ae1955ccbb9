import { Ng<PERSON><PERSON>, NgIf } from '@angular/common';
import { Component, Input } from '@angular/core';

export type StatCardVariant = 'primary' | 'success' | 'warning' | 'info' | 'danger';

@Component({
  selector: 'app-stat-card',
  templateUrl: './stat-card.component.html',
  styleUrls: ['./stat-card.component.css'],
  imports: [NgClass, NgIf],
})
export class StatCardComponent {
  @Input() label: string = '';
  @Input() value: string | number = '';
  @Input() subtitle?: string;
  @Input() iconPath: string = '';
  @Input() variant: StatCardVariant = 'primary';

  get iconBackgroundClass(): string {
    const variants = {
      primary: 'bg-primary/10',
      success: 'bg-green-500/10',
      warning: 'bg-yellow-500/10',
      info: 'bg-blue-500/10',
      danger: 'bg-red-500/10'
    };
    return variants[this.variant];
  }

  get iconColorClass(): string {
    const variants = {
      primary: 'text-primary',
      success: 'text-green-500',
      warning: 'text-yellow-500',
      info: 'text-blue-500',
      danger: 'text-red-500'
    };
    return variants[this.variant];
  }
}
