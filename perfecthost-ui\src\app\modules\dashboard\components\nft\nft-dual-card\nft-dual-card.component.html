<div class="bg-background flex min-h-[420px] flex-col rounded-lg p-8 sm:flex-row">
  <div
    [ngStyle]="{ 'background-image': 'url(' + nft.image + ')' }"
    class="min-h-[200px] w-full flex-1 cursor-pointer rounded-md bg-cover transition duration-150 ease-in-out hover:opacity-75"></div>
  <!-- Details  -->
  <div class="mt-4 flex flex-1 flex-col justify-between space-y-2 ltr:sm:ml-6 rtl:sm:mr-6 md:mt-0">
    <div>
      <small class="text-muted-foreground font-semibold">NFT ID: {{ nft.id }}</small>
      <h2 class="text-foreground text-2xl font-semibold">{{ nft.title }}</h2>
    </div>

    <div class="flex space-x-8">
      <!-- Avatar -->
      <div class="flex items-center space-x-2">
        <img class="mx-auto block h-7 rounded-full sm:mx-0 sm:shrink-0" [src]="nft.avatar" alt="creator face" />
        <div class="flex flex-col">
          <small class="text-muted-foreground text-xs">Creator</small>
          <a href="" class="text-foreground hover:text-primary text-xs font-semibold">
            {{ nft.creator }}
          </a>
        </div>
      </div>

      <!-- Price -->
      <div class="flex items-center space-x-2">
        <div class="bg-primary flex h-7 w-7 shrink-0 items-center justify-center rounded-full text-center">
          <span class="text-primary-foreground text-xs font-semibold">$</span>
        </div>

        <div class="flex flex-col">
          <small class="text-muted-foreground text-xs">Instant Price</small>
          <a href="" class="text-foreground hover:text-primary text-xs font-semibold"> {{ nft.instant_price }} ETH </a>
        </div>
      </div>
    </div>

    <div class="border-border rounded-md border border-dashed p-4 text-center">
      <small class="text-muted-foreground">Last Bid</small>
      <h1 class="text-foreground text-3xl font-semibold">{{ nft.last_bid }} ETH</h1>
      <span class="text-muted-foreground text-lg font-semibold">{{ nft.price | currency }}</span>
      <div class="text-muted-foreground mt-3 text-xs">Ending in</div>
      <div class="text-foreground font-semibold">{{ nft.ending_in }}</div>
    </div>

    <div class="flex items-center justify-between">
      <button
        class="hover:bg-primary-600 bg-primary text-primary-foreground flex-none rounded-md px-4 py-2.5 text-xs font-semibold">
        Place a Bid
      </button>
      <button
        class="lex-none bg-card text-muted-foreground hover:bg-muted hover:text-foreground rounded-md px-4 py-2.5 text-xs font-semibold">
        View Item
      </button>
    </div>
  </div>
</div>
