package cl.ufro.dci.propiedad.service;

import cl.ufro.dci.propiedad.domain.Pais;
import cl.ufro.dci.propiedad.domain.TipoComodidad;
import cl.ufro.dci.propiedad.domain.TipoCocina;
import cl.ufro.dci.propiedad.domain.TipoHabitacion;
import cl.ufro.dci.propiedad.dto.CatalogoDTO;
import cl.ufro.dci.propiedad.dto.PaisDTO;
import cl.ufro.dci.propiedad.repository.PaisRepository;
import cl.ufro.dci.propiedad.repository.TipoComodidadRepository;
import cl.ufro.dci.propiedad.repository.TipoCocinaRepository;
import cl.ufro.dci.propiedad.repository.TipoHabitacionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Servicio que gestiona las operaciones relacionadas con los catálogos del sistema.
 * Proporciona métodos para obtener información de los diferentes catálogos utilizados
 * en la aplicación, como tipos de habitación, tipos de cocina, tipos de comodidades y países.
 */
@Service
public class CatalogoService {

    private final TipoHabitacionRepository tipoHabitacionRepository;
    private final TipoCocinaRepository tipoCocinaRepository;
    private final TipoComodidadRepository tipoComodidadRepository;
    private final PaisRepository paisRepository;

    @Autowired
    public CatalogoService(
            TipoHabitacionRepository tipoHabitacionRepository,
            TipoCocinaRepository tipoCocinaRepository,
            TipoComodidadRepository tipoComodidadRepository,
            PaisRepository paisRepository) {
        this.tipoHabitacionRepository = tipoHabitacionRepository;
        this.tipoCocinaRepository = tipoCocinaRepository;
        this.tipoComodidadRepository = tipoComodidadRepository;
        this.paisRepository = paisRepository;
    }

    public List<CatalogoDTO> getAllTiposHabitacion() {
        return tipoHabitacionRepository.findAll().stream()
                .map(this::convertToCatalogoDTO)
                .collect(Collectors.toList());
    }

    public List<CatalogoDTO> getAllTiposCocina() {
        return tipoCocinaRepository.findAll().stream()
                .map(this::convertToCatalogoDTO)
                .collect(Collectors.toList());
    }

    public List<CatalogoDTO> getAllTiposComodidad() {
        return tipoComodidadRepository.findAll().stream()
                .map(this::convertToCatalogoDTO)
                .collect(Collectors.toList());
    }

    public List<PaisDTO> getAllPaises() {
        return paisRepository.findAll().stream()
                .map(this::convertToPaisDTO)
                .collect(Collectors.toList());
    }

    private CatalogoDTO convertToCatalogoDTO(TipoHabitacion tipoHabitacion) {
        return new CatalogoDTO(tipoHabitacion.getId(), tipoHabitacion.getNombre());
    }

    private CatalogoDTO convertToCatalogoDTO(TipoCocina tipoCocina) {
        return new CatalogoDTO(tipoCocina.getId(), tipoCocina.getNombre());
    }

    private CatalogoDTO convertToCatalogoDTO(TipoComodidad tipoComodidad) {
        return new CatalogoDTO(tipoComodidad.getId(), tipoComodidad.getNombre());
    }

    private PaisDTO convertToPaisDTO(Pais pais) {
        return new PaisDTO(pais.getId(), pais.getNombre(), pais.getCodigo());
    }
}
