package cl.ufro.dci.propiedad.controller;

import cl.ufro.dci.propiedad.dto.CatalogoDTO;
import cl.ufro.dci.propiedad.dto.PaisDTO;
import cl.ufro.dci.propiedad.service.CatalogoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Controlador REST que maneja las peticiones relacionadas con los catálogos del sistema.
 * Proporciona endpoints para obtener información de los diferentes catálogos utilizados
 * en la aplicación, como tipos de habitación, tipos de cocina, tipos de comodidades y países.
 */
@RestController
@RequestMapping("/api/catalogos")
@CrossOrigin(origins = "http://localhost:4200")
public class CatalogoController {

    private final CatalogoService catalogoService;

    @Autowired
    public CatalogoController(CatalogoService catalogoService) {
        this.catalogoService = catalogoService;
    }

    @GetMapping("/tipos-habitacion")
    public ResponseEntity<List<CatalogoDTO>> getAllTiposHabitacion() {
        List<CatalogoDTO> tiposHabitacion = catalogoService.getAllTiposHabitacion();
        return new ResponseEntity<>(tiposHabitacion, HttpStatus.OK);
    }

    @GetMapping("/tipos-cocina")
    public ResponseEntity<List<CatalogoDTO>> getAllTiposCocina() {
        List<CatalogoDTO> tiposCocina = catalogoService.getAllTiposCocina();
        return new ResponseEntity<>(tiposCocina, HttpStatus.OK);
    }

    @GetMapping("/tipos-comodidad")
    public ResponseEntity<List<CatalogoDTO>> getAllTiposComodidad() {
        List<CatalogoDTO> tiposComodidad = catalogoService.getAllTiposComodidad();
        return new ResponseEntity<>(tiposComodidad, HttpStatus.OK);
    }

    @GetMapping("/paises")
    public ResponseEntity<List<PaisDTO>> getAllPaises() {
        List<PaisDTO> paises = catalogoService.getAllPaises();
        return new ResponseEntity<>(paises, HttpStatus.OK);
    }
}
