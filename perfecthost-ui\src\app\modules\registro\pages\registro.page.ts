import { Component } from '@angular/core';
import { UsuarioFormComponent } from '../components/usuario-form/usuario-form.component';

@Component({
  selector: 'app-registro-page',
  standalone: true,
  imports: [UsuarioFormComponent],
  template: `
    <div class="bg-background min-h-screen">
      <div class="container mx-auto px-6 py-8">
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-foreground mb-2">Registro de Usuarios</h1>
          <p class="text-muted-foreground">Crea tu cuenta para comenzar a usar PerfectHost</p>
        </div>
        <div class="bg-card rounded-lg border border-border p-6">
          <app-usuario-form></app-usuario-form>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .container {
      max-width: 1400px;
    }
  `]
})
export class RegistroPage {}
