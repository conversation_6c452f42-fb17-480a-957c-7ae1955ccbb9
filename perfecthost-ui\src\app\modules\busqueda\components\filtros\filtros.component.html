<!-- Modal overlay -->
<div *ngIf="isOpen" class="fixed inset-0 flex items-center justify-center bg-black/40 bg-opacity-50 z-50">
  <!-- Modal container -->
  <div class="bg-white rounded-lg shadow-lg w-full max-w-md mx-4 p-6 relative max-h-[90vh] overflow-y-auto">
    <h2 class="text-xl font-semibold mb-4">Filtrar resultados</h2>

    <!-- Tipo de alojamiento -->
    <fieldset class="mb-4">
      <legend class="font-medium mb-2">Tipo de alojamiento</legend>
      <div class="space-y-2">
        <label *ngFor="let tipo of tiposAlojamiento" class="flex items-center gap-2">
          <input type="checkbox" 
                 [checked]="filtros.tipos.includes(tipo.id)"
                 (change)="toggleTipoAlojamiento(tipo.id)"
                 class="h-4 w-4">
          {{tipo.nombre}}
        </label>
      </div>
    </fieldset>

    <!-- <PERSON><PERSON> de precios -->
    <fieldset class="mb-4">
      <legend class="font-medium mb-2">Ra<PERSON> de precios</legend>
      <div class="flex items-center gap-4">
        <div class="flex-1">
          <label for="precio-min" class="block text-sm text-gray-600 mb-1">Mínimo</label>
          <input type="number" 
                 id="precio-min" 
                 [(ngModel)]="filtros.precioMin" 
                 (change)="validarRangoPrecios()"
                 min="0" 
                 class="w-full p-2 border rounded">
        </div>
        <div class="flex-1">
          <label for="precio-max" class="block text-sm text-gray-600 mb-1">Máximo</label>
          <input type="number" 
                 id="precio-max" 
                 [(ngModel)]="filtros.precioMax" 
                 (change)="validarRangoPrecios()"
                 min="0" 
                 class="w-full p-2 border rounded">
        </div>
      </div>
    </fieldset>

    <!-- Habitaciones -->
    <fieldset class="mb-4">
      <legend class="font-medium mb-2">Habitaciones</legend>
      <select [(ngModel)]="filtros.habitaciones" 
              (change)="onHabitacionesChange()"
              class="w-full p-2 border rounded">
        <option value="1">1</option>
        <option value="2">2</option>
        <option value="3">3</option>
        <option value="4">4+</option>
      </select>
    </fieldset>

    <!-- Servicios -->
    <fieldset class="mb-6">
      <legend class="font-medium mb-2">Servicios</legend>
      <div class="space-y-2">
        <label *ngFor="let servicio of servicios" class="flex items-center gap-2">
          <input type="checkbox"
                 [checked]="filtros.servicios.includes(servicio.id)"
                 (change)="toggleServicio(servicio.id)"
                 class="h-4 w-4">
          {{servicio.nombre}}
        </label>
      </div>
    </fieldset>

    <!-- Botones de acción -->
    <div class="flex justify-end gap-3">
      <button (click)="close()" class="px-4 py-2 border rounded hover:bg-gray-50">
        Cancelar
      </button>
      <button (click)="aplicarFiltros()" class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90">
        Aplicar
      </button>
    </div>

    <!-- Botón de cerrar (X) -->
    <button (click)="close()" class="absolute top-4 right-4 text-gray-500 hover:text-gray-700 hover:cursor-pointer">
      <svg-icon src="assets/icons/heroicons/outline/x.svg" [svgClass]="'h-5 w-5'"></svg-icon>
    </button>
  </div>
</div>