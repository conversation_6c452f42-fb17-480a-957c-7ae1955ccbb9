import { Component, inject, OnInit, DestroyRef, Input } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AngularSvgIconModule } from 'angular-svg-icon';

import { EstadoBusquedaService } from 'src/app/modules/busqueda/services/estado-busqueda.service';
import { ParametrosBusqueda } from 'src/app/modules/busqueda/model/parametros-busqueda';
import { BusquedaValidators } from 'src/app/modules/busqueda/validators/busqueda.validators';
import { HuespedesModalComponent } from 'src/app/modules/busqueda/components/huespedes-modal/huespedes-modal.component';

@Component({
  selector: 'app-buscador',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, AngularSvgIconModule, HuespedesModalComponent],
  templateUrl: './buscador.component.html',
  styleUrl: './buscador.component.css'
})
export class BuscadorComponent implements OnInit {
  private destroyRef = inject(DestroyRef);
  private estadoService = inject(EstadoBusquedaService);
  private router = inject(Router);
  private fb = inject(FormBuilder);
  @Input() mostrarEncabezados = true;
  formularioBusqueda: FormGroup = this.fb.group({
    ubicacion: '',
    fechaLlegada: ['', [BusquedaValidators.fechaActualValidator()]],
    fechaSalida: ['', [BusquedaValidators.fechaActualValidator()]],
    cantidadAdultos: [0, Validators.min(0)],
    cantidadMenores: [0, Validators.min(0)],
    cantidadMascotas: [0, Validators.min(0)]
  }, { validators: BusquedaValidators.fechasValidasValidator() });

  fechaActual = new Date().toISOString().split('T')[0];
  mostrarModal = false;

  ngOnInit(): void {
    this.estadoService.parametros$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(params => this.formularioBusqueda.patchValue(params));
  }

  buscarHospedajes(): void {
    this.formularioBusqueda.updateValueAndValidity();

    console.log('Formulario:', this.formularioBusqueda.value);
    console.log('Estado inválido:', this.formularioBusqueda.invalid);

    if (this.formularioBusqueda.invalid) return;

    console.log(this.formularioBusqueda.get('fechaLlegada')?.value)

    const formulario = this.formularioBusqueda.value;
    const parametros: ParametrosBusqueda = {
      ubicacion: formulario.ubicacion ?? '',
      fechaLlegada: formulario.fechaLlegada ?? this.obtenerFechaActual(),
      fechaSalida: formulario.fechaSalida ?? this.obtenerFechaActual(),
      cantidadAdultos: formulario.cantidadAdultos ?? 1,
      cantidadMenores: formulario.cantidadMenores ?? 0,
      cantidadMascotas: formulario.cantidadMascotas ?? 0
    };

    this.estadoService.actualizarParametros(parametros);
    this.estadoService.ejecutarBusqueda();

    this.router.navigate(['/resultados'], {
      queryParams: {
        ubicacion: parametros.ubicacion,
        fecha_llegada: parametros.fechaLlegada,
        fecha_salida: parametros.fechaSalida,
        adultos: parametros.cantidadAdultos,
        menores: parametros.cantidadMenores,
        mascotas: parametros.cantidadMascotas
      }
    });
  }

  get cantidadTotalHuespedes(): number {
    return (
      (this.formularioBusqueda.get('cantidadAdultos')?.value || 0) +
      (this.formularioBusqueda.get('cantidadMenores')?.value || 0) +
      (this.formularioBusqueda.get('cantidadMascotas')?.value || 0)
    );
  }

  get textoCantidadHuespedes(): string {
    const total = this.cantidadTotalHuespedes;
    return total === 0 ? '¿Cuántos?' : `${total} ${total === 1 ? 'huésped' : 'huéspedes'}`;
  }

  private obtenerFechaActual(): string {
    const today = new Date();
    return today.toISOString().split('T')[0];
  }
}