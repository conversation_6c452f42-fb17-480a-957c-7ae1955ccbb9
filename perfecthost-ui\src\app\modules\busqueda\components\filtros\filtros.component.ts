import { Component, Output, EventEmitter, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AngularSvgIconModule } from 'angular-svg-icon';

@Component({
  selector: 'app-filtros',
  standalone: true,
  imports: [CommonModule, FormsModule, AngularSvgIconModule],
  templateUrl: './filtros.component.html',
  styleUrls: ['./filtros.component.css']
})
export class FiltrosComponent {
  @Input() isOpen = false; // Nueva propiedad para controlar la visibilidad del modal
  @Output() closed = new EventEmitter<void>(); // Nuevo evento para cerrar el modal
  @Output() filtrosCambiados = new EventEmitter<any>();

  // Tipos de alojamiento
  tiposAlojamiento = [
    { id: 'casa', nombre: 'Casa completa' },
    { id: 'apartamento', nombre: 'Apartamento' },
    { id: 'habitacion', nombre: 'Habitación privada' },
    { id: 'hostal', nombre: 'Hostal' },
    { id: 'hotel', nombre: 'Hotel' }
  ];

  // Servicios disponibles
  servicios = [
    { id: 'wifi', nombre: 'Wi-Fi' },
    { id: 'aire', nombre: 'Aire acondicionado' },
    { id: 'piscina', nombre: 'Piscina' },
    { id: 'parking', nombre: 'Parking gratuito' },
    { id: 'cocina', nombre: 'Cocina' },
    { id: 'desayuno', nombre: 'Desayuno incluido' }
  ];

  // Estado inicial de los filtros
  filtros = {
    tipos: [] as string[],
    precioMin: 0,
    precioMax: 1000,
    habitaciones: 1,
    servicios: [] as string[]
  };

  // Métodos para manejar cambios
  toggleTipoAlojamiento(tipo: string): void {
    const index = this.filtros.tipos.indexOf(tipo);
    if (index > -1) {
      this.filtros.tipos.splice(index, 1);
    } else {
      this.filtros.tipos.push(tipo);
    }
  }

  validarRangoPrecios(): void {
    // Asegurar que los valores sean números enteros positivos
    this.filtros.precioMin = Math.max(0, Math.floor(this.filtros.precioMin));
    this.filtros.precioMax = Math.max(0, Math.floor(this.filtros.precioMax));

    // Validar que el mínimo no sea mayor que el máximo
    if (this.filtros.precioMin > this.filtros.precioMax) {
      // Si se excede, ajustar el valor contrario
      if (this.filtros.precioMax < this.filtros.precioMin) {
        this.filtros.precioMax = this.filtros.precioMin;
      } else {
        this.filtros.precioMin = this.filtros.precioMax;
      }
    }
  }

  onHabitacionesChange(): void {
    this.emitirFiltros();
  }

  toggleServicio(servicio: string): void {
    const index = this.filtros.servicios.indexOf(servicio);
    if (index > -1) {
      this.filtros.servicios.splice(index, 1);
    } else {
      this.filtros.servicios.push(servicio);
    }
  }

  aplicarFiltros(): void {
    this.emitirFiltros();
    this.close(); // Cerramos el modal al aplicar
  }

  private emitirFiltros(): void {
    this.filtrosCambiados.emit(this.filtros);
  }

  close(): void {
    this.closed.emit();
  }
}