import { Component, inject, OnInit, DestroyRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { take } from 'rxjs';
import { HttpErrorResponse } from '@angular/common/http';

import { ParametrosBusqueda } from '../model/parametros-busqueda';
import { Hospedaje } from '../../propiedad/models/hospedaje.model';

import { EncabezadoResultadosComponent } from '../components/header-resultados/encabezado-resultados.component';
import { ListaHospedajesComponent } from '../components/lista-hospedajes/lista-hospedajes.component';
import { MapaComponent } from '../components/mapa/mapa.component';

import { BusquedaService } from 'src/app/modules/busqueda/services/busqueda.service';
import { EstadoBusquedaService } from 'src/app/modules/busqueda/services/estado-busqueda.service';
import { PerfecthostFooterComponent } from '../../layout/components/perfecthost-footer/perfecthost-footer.component';

@Component({
  selector: 'app-resultados-page',
  standalone: true,
  imports: [CommonModule, EncabezadoResultadosComponent, MapaComponent, ListaHospedajesComponent, PerfecthostFooterComponent],
  template: `
    <app-encabezado-resultados [mostrarFiltros]="mostrarFiltros" (alternarVisibilidad)="cambiarVisibilidadFiltro()" (filtrosAplicados)="aplicarFiltros($event)"/>

    <ng-container *ngIf="hospedajes.length; else noResultados">
      <div class="resultados">
        <app-lista-hospedajes [hospedajes]="hospedajes" (hospedajeSelected)="irAHospedaje($event)" class="hospedajes"/>
        <app-mapa [hospedajes]="hospedajes" class="mapa"/>
      </div>
    </ng-container>

    <ng-template #noResultados>
      <p class="text-2xl font-bold text-center py-5">No se encontraron hospedajes</p>
    </ng-template>

    <app-perfecthost-footer />
  `,
  styles: [`
    :host {
      --header-height: 80px;
      box-sizing: border-box;
    }

    .resultados {
      display: flex;
      width: 100%;
      padding: 0px;
      min-height: calc(100vh - var(--header-height));
      margin-top: var(--header-height);
      position: relative;
    }

    .hospedajes {
      width: 60%;
      padding: 1rem;
      padding-right: 0;
    }

    .mapa {
      width: 40%;
      padding: 2rem;
      padding-left: 1rem;
      height: calc(100vh - var(--header-height));
      top: var(--header-height);
      position: sticky;
    }
  `]
})
export class ResultadosPage implements OnInit{
  private route: ActivatedRoute = inject(ActivatedRoute);
  private estadoService: EstadoBusquedaService = inject(EstadoBusquedaService);
  private busquedaService: BusquedaService = inject(BusquedaService);
  private readonly destroyRef: DestroyRef = inject(DestroyRef);
  hospedajes: Hospedaje[] = [];
  mostrarFiltros = false;

  ngOnInit(): void {
    this.suscribirseAQueryParams();
    this.suscribirseABusqueda();
  }
  
  private suscribirseAQueryParams(): void {
    this.route.queryParams
      .pipe(take(1), takeUntilDestroyed(this.destroyRef))
      .subscribe(parametros => {
        const parametrosBusqueda: ParametrosBusqueda = {
          ubicacion: parametros['ubicacion'] || '',
          fechaLlegada: parametros['fecha_llegada'] || '',
          fechaSalida: parametros['fecha_salida'] || '',
          cantidadAdultos: parseInt(parametros['adultos']) || 0,
          cantidadMenores: parseInt(parametros['menores']) || 0,
          cantidadMascotas: parseInt(parametros['mascotas']) || 0
        };
        this.estadoService.actualizarParametros(parametrosBusqueda);
        this.buscarHospedajes();
      });
  }

  private suscribirseABusqueda(): void {
    this.estadoService.busqueda$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.buscarHospedajes();
      });
  }

  private buscarHospedajes(): void {
    const parametros: ParametrosBusqueda = this.estadoService.obtenerParametrosActuales();
    this.busquedaService.obtenerHospedajes(parametros).subscribe({
      next: (hospedajes) => this.procesarRespuestaBusqueda(hospedajes),
      error: (error) => this.procesarErrorBusqueda(error)
    });
  }

  private procesarRespuestaBusqueda(hospedajes: Hospedaje[]): void {
    console.log(hospedajes);
    this.hospedajes = hospedajes;
  }
  
  private procesarErrorBusqueda(error: HttpErrorResponse): void {
    this.hospedajes = [];
    alert('No se pudo conectar con el servidor. Error: ' + error);
  }

  cambiarVisibilidadFiltro(): void {
    this.mostrarFiltros = !this.mostrarFiltros;
  }

  aplicarFiltros(filtros: any): void {
    // Implementa la lógica para aplicar los filtros
    console.log('Filtros aplicados:', filtros);
    // Puedes enviar estos filtros al servicio de búsqueda
  }

  irAHospedaje(hospedaje: Hospedaje): void {
    // Implementar la lógica para redirigir a la publicación del hospedaje seleccionado
    console.log('Hospedaje seleccionado: ', hospedaje.hosId);
  }
} 