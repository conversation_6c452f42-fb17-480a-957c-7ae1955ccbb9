<form [formGroup]="formularioBusqueda" (ngSubmit)="buscarHospedajes()" class="max-w-4xl mx-auto">
  <div 
    class="flex items-center gap-4 rounded-full border border-muted bg-white shadow-lg hover:border-primary/50 transition-colors"
    [ngClass]="mostrarEncabezados ? 'p-2' : 'p-1'"
  >
    <div class="w-px"></div>

    <!-- Ubicación -->
    <div class="flex-[1.5] flex flex-col px-1">
      <label *ngIf="mostrarEncabezados" class="text-xs pl-2">Dónde</label>
      <input 
        formControlName="ubicacion"
        type="text" 
        placeholder="Explora destinos" 
        class="parametro-input bg-transparent border-none"
        aria-label="Destino">
    </div>
    <div class="h-8 w-px bg-muted"></div>

    <!-- <PERSON><PERSON> de llegada -->
    <div class="flex-1 flex flex-col px-1">
      <label *ngIf="mostrarEncabezados" class="text-xs pl-2">Llegada</label>
      <input 
        formControlName="fechaLlegada"
        type="date" 
        [min]="fechaActual"
        class="parametro-input hover:cursor-pointer bg-transparent border-none"
        [class.invalid]="formularioBusqueda.get('fechaLlegada')?.invalid && formularioBusqueda.get('fechaLlegada')?.touched"
        aria-label="Fecha de llegada">
    </div>
    <div class="h-8 w-px bg-muted"></div>

    <!-- Fecha de salida -->
    <div class="flex-1 flex flex-col px-1">
      <label *ngIf="mostrarEncabezados" class="text-xs pl-2">Salida</label>
      <input 
        formControlName="fechaSalida"
        type="date" 
        [min]="fechaActual"
        class="parametro-input hover:cursor-pointer bg-transparent border-none"
        [class.invalid]="formularioBusqueda.get('fechaSalida')?.invalid && formularioBusqueda.get('fechaSalida')?.touched"
        aria-label="Fecha de salida">
    </div>
    <div class="h-8 w-px bg-muted"></div>

    <!-- Cantidad de huéspedes y botón de búsqueda-->
    <div class="flex-[1.5] flex items-center px-1">
      <div class="flex-1 flex flex-col">
        <label *ngIf="mostrarEncabezados" class="text-xs pl-2">Quién</label>
        <button
          type="button"
          (click)="mostrarModal = true"
          class="parametro-input hover:cursor-pointer bg-transparent text-left border-none"
          [ngClass]="cantidadTotalHuespedes > 0 ? 'text-foreground' : 'text-muted-foreground'"
          aria-label="Cantidad de huespedes"
          >
            {{ textoCantidadHuespedes }}
        </button>
        
        <app-huespedes-modal
          [isOpen]="mostrarModal"
          [form]="formularioBusqueda"
          (closed)="mostrarModal = false"
        >
        </app-huespedes-modal>
      </div>

      <!-- Botón de búsqueda -->
      <button 
        type="submit" 
        class="hover:cursor-pointer bg-primary text-primary-foreground p-3 rounded-full hover:bg-primary/90 transition-colors"
        [disabled]="formularioBusqueda.invalid"
        aria-label="Buscar alojamientos">
        <svg-icon 
          src="assets/icons/heroicons/outline/magnifying-glass.svg" 
          [svgClass]="mostrarEncabezados ? 'h-5 w-5' : 'h-4 w-4'">
        </svg-icon>
      </button>
    </div>
  </div>
</form>