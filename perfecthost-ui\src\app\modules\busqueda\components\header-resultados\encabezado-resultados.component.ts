import { Component, EventEmitter, Input, Output } from '@angular/core';
import { AngularSvgIconModule } from 'angular-svg-icon';

import { BuscadorComponent } from 'src/app/modules/busqueda/components/buscador/buscador.component';
import { FiltrosComponent } from 'src/app/modules/busqueda/components/filtros/filtros.component';

@Component({
  selector: 'app-encabezado-resultados',
  imports: [AngularSvgIconModule, BuscadorComponent, FiltrosComponent],
  templateUrl: './encabezado-resultados.component.html',
  styleUrl: './encabezado-resultados.component.css'
})
export class EncabezadoResultadosComponent {
  @Input() mostrarFiltros = false;
  @Output() alternarVisibilidad = new EventEmitter<void>();
  @Output() filtrosAplicados = new EventEmitter<any>();

  alternarVisibilidadFiltros(): void {
    this.alternarVisibilidad.emit();
  }

  aplicarFiltros(filtros: any): void {
    this.filtrosAplicados.emit(filtros);
  }
}
