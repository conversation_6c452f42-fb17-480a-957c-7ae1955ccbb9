<!-- login form -->
<form class="my-10 space-y-6" [formGroup]="form" (ngSubmit)="onSubmit()">
  <div class="text-center">
    <h2 class="text-foreground mb-1 text-3xl font-semibold">Hello Again <span class="text-primary">!</span></h2>
    <p class="text-muted-foreground text-sm">Enter your credential to access your account.</p>
  </div>

  <div routerLink="/dashboard">
    <app-button full impact="bold" tone="light" shape="rounded" size="medium">
      <svg-icon src="assets/icons/google-logo.svg" [svgClass]="'h-6 w-6 mr-2'"> </svg-icon>
      Log in with Google
    </app-button>
  </div>

  <div
    class="before:border-muted after:border-muted my-4 flex items-center before:mt-0.5 before:flex-1 before:border-t after:mt-0.5 after:flex-1 after:border-t">
    <p class="text-muted-foreground mx-4 mb-0 text-center text-sm">or</p>
  </div>

  <div class="space-y-3 text-left">
    <div class="form__group">
      <div class="relative">
        <input
          type="text"
          id="email"
          [ngClass]="{ 'is__invalid-input': submitted && f['email'].errors }"
          class="peer block"
          placeholder=" "
          formControlName="email" />
        <label
          for="email"
          [ngClass]="{ 'peer-focus:text-destructive!': submitted && f['email'].errors }"
          class="bg-background text-muted-foreground peer-focus:text-primary absolute top-2 z-10 origin-[0] -translate-y-4 scale-95 transform px-2 text-sm duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-95 peer-focus:px-2 ltr:left-1 rtl:right-1">
          Email address
        </label>
      </div>
      <div *ngIf="submitted && f['email'].errors" class="is__invalid-error">
        <div *ngIf="f['email'].errors['required']">Required field</div>
        <div *ngIf="f['email'].errors['email']">Email must be an email address valid</div>
      </div>
    </div>

    <div class="form__group">
      <div class="relative">
        <input
          [type]="passwordTextType ? 'text' : 'password'"
          id="password"
          [ngClass]="{ 'is__invalid-input': submitted && f['password'].errors }"
          class="peer block"
          placeholder=" "
          formControlName="password" />
        <label
          for="password"
          [ngClass]="{ 'peer-focus:text-destructive!': submitted && f['password'].errors }"
          class="bg-background text-muted-foreground peer-focus:text-primary absolute top-2 z-10 origin-[0] -translate-y-4 scale-95 transform px-2 text-sm duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-95 peer-focus:px-2 ltr:left-1 rtl:right-1">
          Password
        </label>
        <span
          class="text-muted-foreground absolute top-2 cursor-pointer ltr:right-3 rtl:left-3"
          (click)="togglePasswordTextType()">
          <svg-icon
            [src]="
              !passwordTextType
                ? 'assets/icons/heroicons/outline/eye-off.svg'
                : 'assets/icons/heroicons/outline/eye.svg'
            "
            [svgClass]="'h-5 w-5'">
          </svg-icon>
        </span>
      </div>
      <div *ngIf="submitted && f['password'].errors" class="is__invalid-error">
        <div *ngIf="f['password'].errors['required']">Required field</div>
      </div>
    </div>
  </div>

  <div class="mb-2 flex items-center justify-between space-x-3">
    <div class="flex items-center">
      <input id="remember-me" name="remember-me" type="checkbox" />
      <label for="remember-me" class="text-muted-foreground ml-2 block text-sm"> Remember me </label>
    </div>

    <app-button routerLink="/auth/forgot-password" impact="none" tone="primary" shape="rounded" size="small">
      Forgot your password?
    </app-button>
  </div>

  <!-- Submit Button -->
  <div>
    <app-button full impact="bold" tone="primary" shape="rounded" size="medium">Sign in</app-button>
  </div>

  <!-- Sign-up -->
  <div class="text-muted-foreground flex items-center text-sm">
    Not a Member yet?
    <app-button routerLink="/auth/sign-up" impact="none" tone="primary" shape="rounded" size="small">
      Sign up
    </app-button>
  </div>
</form>
