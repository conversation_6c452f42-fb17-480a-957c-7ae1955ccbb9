import { MenuItem } from '../models/menu.model';

export class PerfectHostMenu {
  public static pages: MenuItem[] = [
    {
      group: 'Búsqueda y Propiedades',
      separator: false,
      items: [
        {
          icon: 'assets/icons/heroicons/outline/magnifying-glass.svg',
          label: 'B<PERSON>que<PERSON>',
          route: '/busqueda',
        },
        {
          icon: 'assets/icons/heroicons/outline/home.svg',
          label: 'Propiedades',
          route: '/propiedad',
        },
      ],
    },
    {
      group: 'Reservaciones y Comunicación',
      separator: true,
      items: [
        {
          icon: 'assets/icons/heroicons/outline/calendar-days.svg',
          label: 'Reservaciones',
          route: '/reservacion',
        },
        {
          icon: 'assets/icons/heroicons/outline/chat-bubble-left-right.svg',
          label: 'Mensajería',
          route: '/mensajeria',
        },
      ],
    },
    {
      group: 'Pagos y Recompensas',
      separator: true,
      items: [
        {
          icon: 'assets/icons/heroicons/outline/credit-card.svg',
          label: 'Pagos',
          route: '/pagos',
        },
        {
          icon: 'assets/icons/heroicons/outline/gift.svg',
          label: 'Recompensas',
          route: '/recompensas',
        },
      ],
    },
    {
      group: 'Evaluaciones',
      separator: true,
      items: [
        {
          icon: 'assets/icons/heroicons/outline/star.svg',
          label: 'Evaluaciones',
          route: '/evaluacion',
        },
      ],
    },
  ];
}
