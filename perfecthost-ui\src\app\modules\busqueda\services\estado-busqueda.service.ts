import { Injectable } from '@angular/core';
import { BehaviorSubject, Subject } from 'rxjs';
import { Hospedaje } from 'src/app/modules/propiedad/models/hospedaje.model';
import { ParametrosBusqueda } from 'src/app/modules/busqueda/model/parametros-busqueda';

@Injectable({
  providedIn: 'root'
})
export class EstadoBusquedaService {
  private parametrosSubject = new BehaviorSubject<ParametrosBusqueda>({
    ubicacion: '',
    fechaLlegada: '',
    fechaSalida: '',
    cantidadAdultos: 0,
    cantidadMenores: 0,
    cantidadMascotas: 0
  });
  private resultadosSubject = new BehaviorSubject<Hospedaje[]>([]);
  private busquedaSubject = new Subject<void>();
  
  resultados$ = this.resultadosSubject.asObservable();
  parametros$ = this.parametrosSubject.asObservable();
  busqueda$ = this.busquedaSubject.asObservable();

  actualizarParametros(parametros: ParametrosBusqueda): void {
    this.parametrosSubject.next(parametros);
  }

  actualizarResultados(hospedajes: Hospedaje[]): void {
    this.resultadosSubject.next(hospedajes);
  }

  obtenerParametrosActuales(): ParametrosBusqueda {
    return this.parametrosSubject.getValue();
  }

  ejecutarBusqueda(): void {
    this.busquedaSubject.next();
  }
}
