/**
 * Servicio que maneja la lógica de búsqueda y obtención de hospedajes.
 * <p>
 * Este servicio proporciona métodos para obtener una lista de hospedajes con sus respectivos detalles.
 * Utiliza {@link HospedajeService} para posibles interacciones con servicios relacionados a hospedajes.
 * </p>
 *
 * <AUTHOR>
 * <AUTHOR>
 * @version 1.0
 * @since 2025
 * @see HospedajeService
 */
package cl.ufro.dci.busqueda.service;

import cl.ufro.dci.propiedad.dto.HospedajeDTO;
import cl.ufro.dci.propiedad.service.HospedajeService;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

@Service
public class BusquedaService {
    private final HospedajeService hospedajeService;

    /**
     * Constructor para la inyección de dependencias del servicio de hospedaje.
     *
     * @param hospedajeService Servicio de hospedaje a inyectar
     */
    public BusquedaService(HospedajeService hospedajeService) {
        this.hospedajeService = hospedajeService;
    }

    public List<HospedajeDTO> obtenerListaDeHospedajes(String ubicacion) {
        List<HospedajeDTO> hospedajes = getHospedajesActivos(hospedajeService.getHospedajesByPais(ubicacion));
        if (!hospedajes.isEmpty()) return hospedajes;
    
        hospedajes = getHospedajesActivos(hospedajeService.getHospedajesByCiudad(ubicacion));
        if (!hospedajes.isEmpty()) return hospedajes;
    
        hospedajes = getHospedajesActivos(hospedajeService.getAllHospedajes());
        return hospedajes.isEmpty() ? Collections.emptyList() : hospedajes;
    }
    
    private List<HospedajeDTO> getHospedajesActivos(List<HospedajeDTO> hospedajes) {
        return hospedajes.stream()
                .filter(h -> h.getHosEstado() == 1)
                .collect(Collectors.toList());
    }
}