package cl.ufro.dci.propiedad.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Objeto de Transferencia de Datos (DTO) para representar elementos de catálogos genéricos.
 * Se utiliza para transferir información básica de entidades de catálogo como tipos de habitación,
 * tipos de cocina y tipos de comodidades.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CatalogoDTO {
    private Long id;
    private String nombre;
}
