/* Estilos específicos para el componente stat-card */

/* Transiciones suaves */
.bg-card {
  transition: all 0.2s ease-in-out;
}

/* Hover effect */
.bg-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Animación de entrada */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.bg-card {
  animation: fadeInUp 0.3s ease-out;
}
