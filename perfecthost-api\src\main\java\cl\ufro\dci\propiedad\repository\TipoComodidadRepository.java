package cl.ufro.dci.propiedad.repository;

import cl.ufro.dci.propiedad.domain.TipoComodidad;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * Repositorio para la entidad TipoComodidad.
 * Proporciona métodos para realizar operaciones CRUD sobre los tipos de comodidades.
 */
@Repository
public interface TipoComodidadRepository extends JpaRepository<TipoComodidad, Long> {
}
