import { NgIf } from '@angular/common';
import { Component, Input, ContentChild, TemplateRef, AfterContentInit } from '@angular/core';

@Component({
  selector: 'app-content-card',
  templateUrl: './content-card.component.html',
  styleUrls: ['./content-card.component.css'],
  imports: [NgIf],
})
export class ContentCardComponent implements AfterContentInit {
  @Input() title?: string;
  
  @ContentChild('[slot=actions]', { read: TemplateRef }) actionsTemplate?: TemplateRef<any>;
  
  hasActions = false;

  ngAfterContentInit() {
    this.hasActions = !!this.actionsTemplate;
  }
}
