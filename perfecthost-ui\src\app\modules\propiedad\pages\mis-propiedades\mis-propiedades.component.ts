import {Component, inject, OnInit} from '@angular/core';
import { HospedajeService } from '../../services/hospedaje.service';
import { Hospedaje } from '../../models/hospedaje.model';
import {catchError, finalize, of, tap} from 'rxjs';
import {RouterLink} from '@angular/router';
import {NgForOf, NgIf} from '@angular/common';
import {HospedajeComponent} from '../../components/hospedaje/hospedaje.component';

@Component({
  selector: 'app-mis-propiedades',
  imports: [
    RouterLink,
    NgIf,
    NgForOf,
    HospedajeComponent
  ],
  templateUrl: './mis-propiedades.component.html',
  styleUrl: './mis-propiedades.component.css'
})
export class MisPropiedadesComponent implements OnInit {
  private hospedajeService = inject(HospedajeService)

  hospedajes: Hospedaje[] = []
  cargando = true
  error: string | null = null

  ngOnInit() {
    this.cargarHospedajes()
  }

  cargarHospedajes() {
    this.cargando = true
    this.error = null

    this.hospedajeService
      .getHospedajesByUsuario()
      .pipe(
        tap((hospedajes) => (this.hospedajes = hospedajes)),
        catchError((err) => {
          this.error = err.message || "Error al cargar las propiedades"
          return of([])
        }),
        finalize(() => (this.cargando = false)),
      )
      .subscribe()
  }

  onEstadoCambiado(hospedajeActualizado: Hospedaje) {
    const index = this.hospedajes.findIndex((h) => h.hosId === hospedajeActualizado.hosId)
    if (index !== -1) {
      this.hospedajes[index] = hospedajeActualizado
    }
  }

  onEliminado(hospedajeId: number) {
    this.hospedajes = this.hospedajes.filter((h) => h.hosId !== hospedajeId)
  }
}
