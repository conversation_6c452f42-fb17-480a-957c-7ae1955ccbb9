<div class="flex-col rounded-lg bg-background px-8 py-8 sm:flex-row">
  <div class="mb-4 flex items-center justify-between">
    <div class="flex-col items-center">
      <h3 class="text-md font-bold text-foreground">Active Auctions</h3>
      <span class="text-xs text-muted-foreground">Updated 37 minutes ago</span>
    </div>
    <button
      class="flex-none rounded-md bg-card px-4 py-2.5 text-xs font-semibold text-muted-foreground hover:bg-muted hover:text-foreground">
      History
    </button>
  </div>

  <div class="relative overflow-x-auto">
    <table class="w-full table-auto">
      <!--Table head-->
      <thead class="text-xs uppercase text-muted-foreground">
        <tr>
          <th class="py-3 text-left">Item</th>
          <th class="py-3 text-right">Open Price</th>
          <th class="py-3 text-right">Price $</th>
          <th class="py-3 text-right">Recent Offer</th>
          <th class="py-3 text-right">Time Left</th>
          <th class="py-3 text-right">View</th>
        </tr>
      </thead>
      <!--end Table head-->
      <!--Table body-->
      <tbody>
        <tr
          *ngFor="let auction of activeAuction"
          [auction]="auction"
          nft-auctions-table-item
          class="border-b border-dashed border-border hover:bg-card"></tr>
      </tbody>
      <!--end::Table body-->
    </table>
  </div>
</div>
