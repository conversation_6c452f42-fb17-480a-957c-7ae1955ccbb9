import {inject, Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {catchError, map, Observable, of, throwError} from 'rxjs';
import { Catalogo, Hospedaje, <PERSON>is, Tipo } from 'src/app/modules/propiedad/models/hospedaje.model';

@Injectable({
  providedIn: 'root'
})
export class HospedajeService {
  private http = inject(HttpClient)
  private apiUrl = 'http://localhost:8080/api'

  // ID del usuario actual (en una aplicación real, esto vendría de un servicio de autenticación)
  private usuarioId = 1

  getHospedajesByUsuario(): Observable<Hospedaje[]> {
    return this.http.get<Hospedaje[]>(`${this.apiUrl}/hospedajes/usuario/${this.usuarioId}`).pipe(
      catchError((error) => {
        console.error("Error obteniendo hospedajes:", error)
        return throwError(() => new Error("Error al cargar los hospedajes. Por favor, intente nuevamente."))
      }),
    )
  }

  getHospedajeById(id: number): Observable<Hospedaje> {
    return this.http.get<Hospedaje>(`${this.apiUrl}/hospedajes/${id}`).pipe(
      catchError((error) => {
        console.error("Error obteniendo hospedaje:", error)
        return throwError(() => new Error("Error al cargar el hospedaje. Por favor, intente nuevamente."))
      }),
    )
  }

  getTipos(): Observable<Tipo[]> {
    return this.http.get<Tipo[]>(`${this.apiUrl}/tipos`).pipe(
      catchError((error) => {
        console.error("Error obteniendo tipos:", error)
        return of([])
      }),
    )
  }

  getTiposHabitacion(): Observable<Catalogo[]> {
    return this.http.get<Catalogo[]>(`${this.apiUrl}/catalogos/tipos-habitacion`).pipe(
      catchError((error) => {
        console.error("Error obteniendo tipos de habitación:", error)
        return of([])
      }),
    )
  }

  getTiposCocina(): Observable<Catalogo[]> {
    return this.http.get<Catalogo[]>(`${this.apiUrl}/catalogos/tipos-cocina`).pipe(
      catchError((error) => {
        console.error("Error obteniendo tipos de cocina:", error)
        return of([])
      }),
    )
  }

  getTiposComodidad(): Observable<Catalogo[]> {
    return this.http.get<Catalogo[]>(`${this.apiUrl}/catalogos/tipos-comodidad`).pipe(
      catchError((error) => {
        console.error("Error obteniendo tipos de comodidad:", error)
        return of([])
      }),
    )
  }

  getPaises(): Observable<Pais[]> {
    return this.http.get<Pais[]>(`${this.apiUrl}/catalogos/paises`).pipe(
      catchError((error) => {
        console.error("Error obteniendo países:", error)
        return of([])
      }),
    )
  }

  getTipoNombre(tipoId: number): Observable<string> {
    return this.getTipos().pipe(
      map((tipos) => {
        const tipo = tipos.find((t) => t.tipoId === tipoId)
        return tipo ? tipo.nombre : "Desconocido"
      }),
    )
  }

  cambiarEstadoHospedaje(hospedajeId: number, estado: number): Observable<Hospedaje> {
    return this.http.patch<Hospedaje>(`${this.apiUrl}/hospedajes/${hospedajeId}/estado`, { estado }).pipe(
      catchError((error) => {
        console.error("Error cambiando estado:", error)
        return throwError(() => new Error("Error al cambiar el estado. Por favor, intente nuevamente."))
      }),
    )
  }

  eliminarHospedaje(hospedajeId: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/hospedajes/${hospedajeId}`).pipe(
      catchError((error) => {
        console.error("Error eliminando hospedaje:", error)
        return throwError(() => new Error("Error al eliminar el hospedaje. Por favor, intente nuevamente."))
      }),
    )
  }

  crearHospedaje(hospedaje: Partial<Hospedaje>): Observable<Hospedaje> {
    return this.http.post<Hospedaje>(`${this.apiUrl}/hospedajes`, hospedaje).pipe(
      catchError((error) => {
        console.error("Error creando hospedaje:", error)
        return throwError(() => new Error("Error al crear el hospedaje. Por favor, intente nuevamente."))
      }),
    )
  }

  actualizarHospedaje(id: number, hospedaje: Partial<Hospedaje>): Observable<Hospedaje> {
    return this.http.put<Hospedaje>(`${this.apiUrl}/hospedajes/${id}`, hospedaje).pipe(
      catchError((error) => {
        console.error("Error actualizando hospedaje:", error)
        return throwError(() => new Error("Error al actualizar el hospedaje. Por favor, intente nuevamente."))
      }),
    )
  }
}
