package cl.ufro.dci.propiedad.service;

import cl.ufro.dci.propiedad.domain.*;
import cl.ufro.dci.propiedad.dto.HospedajeCreateDTO;
import cl.ufro.dci.propiedad.dto.HospedajeDTO;
import cl.ufro.dci.propiedad.exception.ResourceNotFoundException;
import cl.ufro.dci.propiedad.repository.*;
import cl.ufro.dci.registro.domain.Usuario;
import cl.ufro.dci.registro.repository.UsuarioRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Servicio que gestiona las operaciones relacionadas con los hospedajes.
 * Proporciona métodos para crear, leer, actualizar y eliminar hospedajes,
 * así como para realizar búsquedas y filtrados específicos.
 */
@Service
public class HospedajeService {

    private final HospedajeRepository hospedajeRepository;
    private final TipoRepository tipoRepository;
    private final TipoHabitacionRepository tipoHabitacionRepository;
    private final TipoCocinaRepository tipoCocinaRepository;
    private final TipoComodidadRepository tipoComodidadRepository;
    private final UsuarioRepository usuarioRepository;
    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Autowired
    public HospedajeService(
            HospedajeRepository hospedajeRepository,
            TipoRepository tipoRepository,
            TipoHabitacionRepository tipoHabitacionRepository,
            TipoCocinaRepository tipoCocinaRepository,
            TipoComodidadRepository tipoComodidadRepository,
            UsuarioRepository usuarioRepository) {
        this.hospedajeRepository = hospedajeRepository;
        this.tipoRepository = tipoRepository;
        this.tipoHabitacionRepository = tipoHabitacionRepository;
        this.tipoCocinaRepository = tipoCocinaRepository;
        this.tipoComodidadRepository = tipoComodidadRepository;
        this.usuarioRepository = usuarioRepository;
    }

    public List<HospedajeDTO> getAllHospedajes() {
        return hospedajeRepository.findAll().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    public HospedajeDTO getHospedajeById(Long id) {
        Hospedaje hospedaje = hospedajeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Hospedaje no encontrado con id: " + id));
        return convertToDTO(hospedaje);
    }

    public List<HospedajeDTO> getHospedajesByUsuario(Long usuarioId) {
        Usuario usuario = usuarioRepository.findById(usuarioId)
                .orElseThrow(() -> new ResourceNotFoundException("Usuario no encontrado con id: " + usuarioId));

        return hospedajeRepository.findByUsuario(usuario).stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    public List<HospedajeDTO> getHospedajesByPais(String pais) {
        return hospedajeRepository.findByHosPais(pais).stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    public List<HospedajeDTO> getHospedajesByCiudad(String ciudad) {
        return hospedajeRepository.findByHosCiudad(ciudad).stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    public List<HospedajeDTO> getHospedajesByEstado(int estado) {
        return hospedajeRepository.findByHosEstado(estado).stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    public HospedajeDTO createHospedaje(HospedajeCreateDTO hospedajeDTO) {
        Hospedaje hospedaje = new Hospedaje();

        // Buscar el tipo de hospedaje
        tipoHospedaje tipoHospedajeObj = tipoRepository.findById(hospedajeDTO.getHosTipoHospedajeId())
                .orElseThrow(() -> new ResourceNotFoundException("Tipo de hospedaje no encontrado con id: " + hospedajeDTO.getHosTipoHospedajeId()));

        // Buscar el tipo de habitación
        TipoHabitacion tipoHabitacion = tipoHabitacionRepository.findById(hospedajeDTO.getHosTipoHabitacionId())
                .orElseThrow(() -> new ResourceNotFoundException("Tipo de habitación no encontrado con id: " + hospedajeDTO.getHosTipoHabitacionId()));

        // Buscar el tipo de cocina
        TipoCocina tipoCocina = tipoCocinaRepository.findById(hospedajeDTO.getHosTipoCocinaId())
                .orElseThrow(() -> new ResourceNotFoundException("Tipo de cocina no encontrado con id: " + hospedajeDTO.getHosTipoCocinaId()));

        // Buscar el tipo de comodidad
        TipoComodidad tipoComodidad = tipoComodidadRepository.findById(hospedajeDTO.getHosComodidadesId())
                .orElseThrow(() -> new ResourceNotFoundException("Tipo de comodidad no encontrado con id: " + hospedajeDTO.getHosComodidadesId()));

        // Buscar el usuario
        Usuario usuario = usuarioRepository.findById(hospedajeDTO.getUsuarioId())
                .orElseThrow(() -> new ResourceNotFoundException("Usuario no encontrado con id: " + hospedajeDTO.getUsuarioId()));

        // Establecer campos básicos
        hospedaje.setHosNombre(hospedajeDTO.getHosNombre());
        hospedaje.setHosCapacidad(hospedajeDTO.getHosCapacidad());
        hospedaje.setHosHabitaciones(hospedajeDTO.getHosHabitaciones());
        hospedaje.setHosBanios(hospedajeDTO.getHosBanios());
        hospedaje.setHosPiscinas(hospedajeDTO.getHosPiscinas());
        hospedaje.setHosWifi(hospedajeDTO.isHosWifi());
        hospedaje.setPolitica(hospedajeDTO.getPolitica());
        hospedaje.setHosDescripcion(hospedajeDTO.getHosDescripcion());
        hospedaje.setHosImagen(hospedajeDTO.getHosImagen());
        hospedaje.setHosPrecio(hospedajeDTO.getHosPrecio());
        hospedaje.setHosEstado(1); // Por defecto, estado activo

        // Establecer dirección
        hospedaje.setHosPais(hospedajeDTO.getHosPais());
        hospedaje.setHosRegion(hospedajeDTO.getHosRegion());
        hospedaje.setHosCiudad(hospedajeDTO.getHosCiudad());
        hospedaje.setHosProvincia(hospedajeDTO.getHosProvincia());
        hospedaje.setHosComuna(hospedajeDTO.getHosComuna());
        hospedaje.setHosCalle(hospedajeDTO.getHosCalle());
        hospedaje.setHosNumero(hospedajeDTO.getHosNumero());
        hospedaje.setHosPiso(hospedajeDTO.getHosPiso());
        hospedaje.setHosDpto(hospedajeDTO.getHosDpto());
        hospedaje.setHosCodigoPostal(hospedajeDTO.getHosCodigoPostal());
        hospedaje.setHosLatitud(hospedajeDTO.getHosLatitud());
        hospedaje.setHosLongitud(hospedajeDTO.getHosLongitud());

        // Construir dirección completa
        String direccionCompleta = hospedajeDTO.getHosCalle() + " " + hospedajeDTO.getHosNumero();
        if (hospedajeDTO.getHosPiso() != null && !hospedajeDTO.getHosPiso().isEmpty()) {
            direccionCompleta += ", Piso " + hospedajeDTO.getHosPiso();
        }
        if (hospedajeDTO.getHosDpto() != null && !hospedajeDTO.getHosDpto().isEmpty()) {
            direccionCompleta += ", Dpto " + hospedajeDTO.getHosDpto();
        }
        direccionCompleta += ", " + hospedajeDTO.getHosComuna() + ", " + hospedajeDTO.getHosCiudad() + ", " + hospedajeDTO.getHosPais();
        hospedaje.setHosDireccionCompleta(direccionCompleta);

        // Establecer fechas
        LocalDateTime now = LocalDateTime.now();
        hospedaje.setHosFechaCreacion(now.format(formatter));
        hospedaje.setHosFechaModificacion(now.format(formatter));

        // Establecer relaciones
        hospedaje.setHosTipoHospedaje(tipoHospedajeObj);
        hospedaje.setHosTipoHabitacion(tipoHabitacion);
        hospedaje.setHosTipoCocina(tipoCocina);
        hospedaje.setHosComodidades(tipoComodidad);
        hospedaje.setUsuario(usuario);

        Hospedaje savedHospedaje = hospedajeRepository.save(hospedaje);
        return convertToDTO(savedHospedaje);
    }

    public HospedajeDTO updateHospedaje(Long id, HospedajeCreateDTO hospedajeDTO) {
        Hospedaje hospedaje = hospedajeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Hospedaje no encontrado con id: " + id));

        // Buscar el tipo de hospedaje si se proporciona
        if (hospedajeDTO.getHosTipoHospedajeId() != null) {
            tipoHospedaje tipoHospedajeObj = tipoRepository.findById(hospedajeDTO.getHosTipoHospedajeId())
                    .orElseThrow(() -> new ResourceNotFoundException("Tipo de hospedaje no encontrado con id: " + hospedajeDTO.getHosTipoHospedajeId()));
            hospedaje.setHosTipoHospedaje(tipoHospedajeObj);
        }

        // Buscar el tipo de habitación si se proporciona
        if (hospedajeDTO.getHosTipoHabitacionId() != null) {
            TipoHabitacion tipoHabitacion = tipoHabitacionRepository.findById(hospedajeDTO.getHosTipoHabitacionId())
                    .orElseThrow(() -> new ResourceNotFoundException("Tipo de habitación no encontrado con id: " + hospedajeDTO.getHosTipoHabitacionId()));
            hospedaje.setHosTipoHabitacion(tipoHabitacion);
        }

        // Buscar el tipo de cocina si se proporciona
        if (hospedajeDTO.getHosTipoCocinaId() != null) {
            TipoCocina tipoCocina = tipoCocinaRepository.findById(hospedajeDTO.getHosTipoCocinaId())
                    .orElseThrow(() -> new ResourceNotFoundException("Tipo de cocina no encontrado con id: " + hospedajeDTO.getHosTipoCocinaId()));
            hospedaje.setHosTipoCocina(tipoCocina);
        }

        // Buscar el tipo de comodidad si se proporciona
        if (hospedajeDTO.getHosComodidadesId() != null) {
            TipoComodidad tipoComodidad = tipoComodidadRepository.findById(hospedajeDTO.getHosComodidadesId())
                    .orElseThrow(() -> new ResourceNotFoundException("Tipo de comodidad no encontrado con id: " + hospedajeDTO.getHosComodidadesId()));
            hospedaje.setHosComodidades(tipoComodidad);
        }

        // Actualizar campos básicos
        hospedaje.setHosNombre(hospedajeDTO.getHosNombre());
        hospedaje.setHosCapacidad(hospedajeDTO.getHosCapacidad());
        hospedaje.setHosHabitaciones(hospedajeDTO.getHosHabitaciones());
        hospedaje.setHosBanios(hospedajeDTO.getHosBanios());
        hospedaje.setHosPiscinas(hospedajeDTO.getHosPiscinas());
        hospedaje.setHosWifi(hospedajeDTO.isHosWifi());
        hospedaje.setPolitica(hospedajeDTO.getPolitica());
        hospedaje.setHosDescripcion(hospedajeDTO.getHosDescripcion());
        hospedaje.setHosImagen(hospedajeDTO.getHosImagen());
        hospedaje.setHosPrecio(hospedajeDTO.getHosPrecio());

        // Actualizar dirección
        hospedaje.setHosPais(hospedajeDTO.getHosPais());
        hospedaje.setHosRegion(hospedajeDTO.getHosRegion());
        hospedaje.setHosCiudad(hospedajeDTO.getHosCiudad());
        hospedaje.setHosProvincia(hospedajeDTO.getHosProvincia());
        hospedaje.setHosComuna(hospedajeDTO.getHosComuna());
        hospedaje.setHosCalle(hospedajeDTO.getHosCalle());
        hospedaje.setHosNumero(hospedajeDTO.getHosNumero());
        hospedaje.setHosPiso(hospedajeDTO.getHosPiso());
        hospedaje.setHosDpto(hospedajeDTO.getHosDpto());
        hospedaje.setHosCodigoPostal(hospedajeDTO.getHosCodigoPostal());
        hospedaje.setHosLatitud(hospedajeDTO.getHosLatitud());
        hospedaje.setHosLongitud(hospedajeDTO.getHosLongitud());

        // Reconstruir dirección completa
        String direccionCompleta = hospedajeDTO.getHosCalle() + " " + hospedajeDTO.getHosNumero();
        if (hospedajeDTO.getHosPiso() != null && !hospedajeDTO.getHosPiso().isEmpty()) {
            direccionCompleta += ", Piso " + hospedajeDTO.getHosPiso();
        }
        if (hospedajeDTO.getHosDpto() != null && !hospedajeDTO.getHosDpto().isEmpty()) {
            direccionCompleta += ", Dpto " + hospedajeDTO.getHosDpto();
        }
        direccionCompleta += ", " + hospedajeDTO.getHosComuna() + ", " + hospedajeDTO.getHosCiudad() + ", " + hospedajeDTO.getHosPais();
        hospedaje.setHosDireccionCompleta(direccionCompleta);

        // Actualizar fecha de modificación
        hospedaje.setHosFechaModificacion(LocalDateTime.now().format(formatter));

        Hospedaje updatedHospedaje = hospedajeRepository.save(hospedaje);
        return convertToDTO(updatedHospedaje);
    }

    public HospedajeDTO cambiarEstadoHospedaje(Long id, int nuevoEstado) {
        Hospedaje hospedaje = hospedajeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Hospedaje no encontrado con id: " + id));

        hospedaje.setHosEstado(nuevoEstado);
        hospedaje.setHosFechaModificacion(LocalDateTime.now().format(formatter));

        Hospedaje updatedHospedaje = hospedajeRepository.save(hospedaje);
        return convertToDTO(updatedHospedaje);
    }

    public void deleteHospedaje(Long id) {
        if (!hospedajeRepository.existsById(id)) {
            throw new ResourceNotFoundException("Hospedaje no encontrado con id: " + id);
        }
        hospedajeRepository.deleteById(id);
    }

    private HospedajeDTO convertToDTO(Hospedaje hospedaje) {
        HospedajeDTO dto = new HospedajeDTO();
        dto.setHosId(hospedaje.getHosId());
        dto.setHosNombre(hospedaje.getHosNombre());
        dto.setHosCapacidad(hospedaje.getHosCapacidad());
        dto.setHosHabitaciones(hospedaje.getHosHabitaciones());
        dto.setHosBanios(hospedaje.getHosBanios());
        dto.setHosPiscinas(hospedaje.getHosPiscinas());
        dto.setHosWifi(hospedaje.isHosWifi());
        dto.setPolitica(hospedaje.getPolitica());
        dto.setHosDescripcion(hospedaje.getHosDescripcion());
        dto.setHosImagen(hospedaje.getHosImagen());
        dto.setHosEstado(hospedaje.getHosEstado());
        dto.setHosPrecio(hospedaje.getHosPrecio());
        dto.setHosPais(hospedaje.getHosPais());
        dto.setHosRegion(hospedaje.getHosRegion());
        dto.setHosCiudad(hospedaje.getHosCiudad());
        dto.setHosProvincia(hospedaje.getHosProvincia());
        dto.setHosComuna(hospedaje.getHosComuna());
        dto.setHosCalle(hospedaje.getHosCalle());
        dto.setHosNumero(hospedaje.getHosNumero());
        dto.setHosPiso(hospedaje.getHosPiso());
        dto.setHosDpto(hospedaje.getHosDpto());
        dto.setHosCodigoPostal(hospedaje.getHosCodigoPostal());
        dto.setHosLatitud(hospedaje.getHosLatitud());
        dto.setHosLongitud(hospedaje.getHosLongitud());
        dto.setHosDireccionCompleta(hospedaje.getHosDireccionCompleta());
        dto.setHosFechaCreacion(hospedaje.getHosFechaCreacion());
        dto.setHosFechaModificacion(hospedaje.getHosFechaModificacion());

        if (hospedaje.getHosTipoHospedaje() != null) {
            dto.setHosTipoHospedajeId(hospedaje.getHosTipoHospedaje().getTipoId());
            dto.setHosTipoHospedajeNombre(hospedaje.getHosTipoHospedaje().getNombre());
        }

        if (hospedaje.getHosTipoHabitacion() != null) {
            dto.setHosTipoHabitacionId(hospedaje.getHosTipoHabitacion().getId());
            dto.setHosTipoHabitacionNombre(hospedaje.getHosTipoHabitacion().getNombre());
        }

        if (hospedaje.getHosTipoCocina() != null) {
            dto.setHosTipoCocinaId(hospedaje.getHosTipoCocina().getId());
            dto.setHosTipoCocianNombre(hospedaje.getHosTipoCocina().getNombre());
        }

        if (hospedaje.getHosComodidades() != null) {
            dto.setHosComodidadesId(hospedaje.getHosComodidades().getId());
            dto.setHosComodidadesNombre(hospedaje.getHosComodidades().getNombre());
        }

        if (hospedaje.getUsuario() != null) {
            dto.setUsuarioId(hospedaje.getUsuario().getUsuId());
        }

        return dto;
    }
}
