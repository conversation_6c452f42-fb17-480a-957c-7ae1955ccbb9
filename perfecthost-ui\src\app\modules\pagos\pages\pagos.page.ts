import { Component } from '@angular/core';
import { MetodoPagoComponent } from '../components/metodo-pago/metodo-pago.component';
import { TransaccionComponent } from '../components/transaccion/transaccion.component';

@Component({
  selector: 'app-pagos-page',
  standalone: true,
  imports: [MetodoPagoComponent, TransaccionComponent],
  template: `
    <div class="bg-background min-h-screen">
      <div class="container mx-auto px-6 py-8">
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-foreground mb-2">Gestión de Pagos</h1>
          <p class="text-muted-foreground">Administra tus métodos de pago y transacciones</p>
        </div>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div class="bg-card rounded-lg border border-border p-6">
            <h2 class="text-lg font-semibold text-foreground mb-4">Métodos de Pago</h2>
            <app-metodo-pago></app-metodo-pago>
          </div>
          <div class="bg-card rounded-lg border border-border p-6">
            <h2 class="text-lg font-semibold text-foreground mb-4">Transacciones</h2>
            <app-transaccion></app-transaccion>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .container {
      max-width: 1400px;
    }
  `]
})
export class PagosPage {}
