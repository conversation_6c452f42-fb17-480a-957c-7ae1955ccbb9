package cl.ufro.dci.propiedad.controller;

import cl.ufro.dci.propiedad.dto.HospedajeCreateDTO;
import cl.ufro.dci.propiedad.dto.HospedajeDTO;
import cl.ufro.dci.propiedad.service.HospedajeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Controlador REST que maneja las peticiones relacionadas con los hospedajes.
 * Proporciona endpoints para crear, leer, actualizar y eliminar hospedajes,
 * así como para realizar búsquedas por diferentes criterios.
 */
@RestController
@RequestMapping("/api/hospedajes")
@CrossOrigin(origins = "http://localhost:4200")
public class HospedajeController {

    private final HospedajeService hospedajeService;

    @Autowired
    public HospedajeController(HospedajeService hospedajeService) {
        this.hospedajeService = hospedajeService;
    }

    @GetMapping
    public ResponseEntity<List<HospedajeDTO>> getAllHospedajes() {
        List<HospedajeDTO> hospedajes = hospedajeService.getAllHospedajes();
        return new ResponseEntity<>(hospedajes, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    public ResponseEntity<HospedajeDTO> getHospedajeById(@PathVariable Long id) {
        HospedajeDTO hospedaje = hospedajeService.getHospedajeById(id);
        return new ResponseEntity<>(hospedaje, HttpStatus.OK);
    }

    @GetMapping("/usuario/{usuarioId}")
    public ResponseEntity<List<HospedajeDTO>> getHospedajesByUsuario(@PathVariable Long usuarioId) {
        List<HospedajeDTO> hospedajes = hospedajeService.getHospedajesByUsuario(usuarioId);
        return new ResponseEntity<>(hospedajes, HttpStatus.OK);
    }

    @GetMapping("/pais/{pais}")
    public ResponseEntity<List<HospedajeDTO>> getHospedajesByPais(@PathVariable String pais) {
        List<HospedajeDTO> hospedajes = hospedajeService.getHospedajesByPais(pais);
        return new ResponseEntity<>(hospedajes, HttpStatus.OK);
    }

    @GetMapping("/ciudad/{ciudad}")
    public ResponseEntity<List<HospedajeDTO>> getHospedajesByCiudad(@PathVariable String ciudad) {
        List<HospedajeDTO> hospedajes = hospedajeService.getHospedajesByCiudad(ciudad);
        return new ResponseEntity<>(hospedajes, HttpStatus.OK);
    }

    @GetMapping("/estado/{estado}")
    public ResponseEntity<List<HospedajeDTO>> getHospedajesByEstado(@PathVariable int estado) {
        List<HospedajeDTO> hospedajes = hospedajeService.getHospedajesByEstado(estado);
        return new ResponseEntity<>(hospedajes, HttpStatus.OK);
    }

    @PostMapping
    public ResponseEntity<HospedajeDTO> createHospedaje(@RequestBody HospedajeCreateDTO hospedajeDTO) {
        HospedajeDTO createdHospedaje = hospedajeService.createHospedaje(hospedajeDTO);
        return new ResponseEntity<>(createdHospedaje, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    public ResponseEntity<HospedajeDTO> updateHospedaje(@PathVariable Long id, @RequestBody HospedajeCreateDTO hospedajeDTO) {
        HospedajeDTO updatedHospedaje = hospedajeService.updateHospedaje(id, hospedajeDTO);
        return new ResponseEntity<>(updatedHospedaje, HttpStatus.OK);
    }

    @PatchMapping("/{id}/estado")
    public ResponseEntity<HospedajeDTO> cambiarEstadoHospedaje(@PathVariable Long id, @RequestBody Map<String, Integer> estado) {
        Integer nuevoEstado = estado.get("estado");
        if (nuevoEstado == null) {
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }

        HospedajeDTO updatedHospedaje = hospedajeService.cambiarEstadoHospedaje(id, nuevoEstado);
        return new ResponseEntity<>(updatedHospedaje, HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteHospedaje(@PathVariable Long id) {
        hospedajeService.deleteHospedaje(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
