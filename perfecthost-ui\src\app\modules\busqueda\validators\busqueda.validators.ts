import { AbstractControl, FormGroup, ValidationErrors, ValidatorFn } from '@angular/forms';

export class BusquedaValidators{
  static fechaActualValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const fecha = control.value;

      if (!fecha) return null;
      
      const [year, month, day] = fecha.split('-').map(Number);
      const fechaDate = new Date(year, month - 1, day, 0, 0, 0, 0);

      if (isNaN(fechaDate.getTime())) return { fechaInvalida: true };
      
      const fechaHoy = new Date();
      const fechaHoyNormalizada = new Date(
        fechaHoy.getFullYear(),
        fechaHoy.getMonth(),
        fechaHoy.getDate(),
        0, 0, 0, 0
      );
      
      if (fechaDate < fechaHoyNormalizada) return { fechaPasada: true };
      
      return null;
    };
  }

  static fechasValidasValidator(): ValidatorFn {
    return (formGroup: AbstractControl): ValidationErrors | null => {
      const fechaLlegada = formGroup.get('fechaLlegada')?.value;
      const fechaSalida = formGroup.get('fechaSalida')?.value;

      // Si alguna fecha no está definida, no validamos aún
      if (!fechaLlegada || !fechaSalida) return null;

      const fechaLlegadaDate = new Date(fechaLlegada);
      const fechaSalidaDate = new Date(fechaSalida);

      // Verificar si las fechas son válidas
      if (isNaN(fechaLlegadaDate.getTime()) || isNaN(fechaSalidaDate.getTime())) {
        return { fechasInvalidas: true };
      }

      // Validar que fechaLlegada no sea posterior a fechaSalida
      if (fechaLlegadaDate > fechaSalidaDate) {
        return { fechaLlegadaPosterior: true };
      }

      return null;
    };
  }
}