package cl.ufro.dci.propiedad.domain;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Entidad que representa los países disponibles para la ubicación de hospedajes.
 * Esta clase almacena información básica sobre los países, incluyendo su nombre
 * y código ISO.
 */
@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Pais {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String nombre;

    /**
     * Código ISO del país (generalmente de dos letras).
     */
    private String codigo;
}
