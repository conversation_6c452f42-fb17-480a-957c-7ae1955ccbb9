import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { RouterLink, RouterLinkActive } from '@angular/router';
import { AngularSvgIconModule } from 'angular-svg-icon';
import { SubMenuItem } from 'src/app/core/models/menu.model';
import { PerfectHostMenuService } from '../../../services/perfecthost-menu.service';

@Component({
  selector: 'app-perfecthost-sidebar-submenu',
  templateUrl: './perfecthost-sidebar-submenu.component.html',
  styleUrls: ['./perfecthost-sidebar-submenu.component.css'],
  imports: [Ng<PERSON>lass, NgFor, NgTemplateOutlet, RouterLinkActive, RouterLink, AngularSvgIconModule],
})
export class PerfectHostSidebarSubmenuComponent implements OnInit {
  @Input() public submenu = <SubMenuItem>{};

  constructor(public perfectHostMenuService: PerfectHostMenuService) {}

  ngOnInit(): void {}

  public toggleMenu(menu: any) {
    this.perfectHostMenuService.toggleSubMenu(menu);
  }

  private collapse(items: Array<any>) {
    items.forEach((item) => {
      item.expanded = false;
      if (item.children) this.collapse(item.children);
    });
  }
}
