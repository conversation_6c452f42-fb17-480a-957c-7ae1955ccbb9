version: '3.8'

services:
  # Servicio del backend (Spring Boot)
  perfecthost-api:
    build:
      context: ./perfecthost-api
      dockerfile: Dockerfile
    container_name: perfecthost-api
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SERVER_PORT=8080
    networks:
      - perfecthost-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Servicio del frontend (Angular + nginx)
  perfecthost-ui:
    build:
      context: ./perfecthost-ui
      dockerfile: Dockerfile
    container_name: perfecthost-ui
    ports:
      - "80:80"
    depends_on:
      - perfecthost-api
    networks:
      - perfecthost-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

networks:
  perfecthost-network:
    driver: bridge
