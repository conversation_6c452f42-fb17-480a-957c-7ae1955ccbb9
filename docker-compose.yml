version: '3.8'

services:
  # Base de datos PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: ${POSTGRES_CONTAINER_NAME}
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - "${POSTGRES_PORT}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - ${NETWORK_NAME}
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # API Spring Boot
  api:
    build:
      context: ./perfecthost-api
      dockerfile: Dockerfile
    container_name: ${API_CONTAINER_NAME}
    environment:
      SPRING_PROFILES_ACTIVE: ${SPRING_PROFILES_ACTIVE}
      SPRING_DATASOURCE_URL: jdbc:postgresql://${POSTGRES_HOST}:5432/${POSTGRES_DB}
      SPRING_DATASOURCE_USERNAME: ${POSTGRES_USER}
      SPRING_DATASOURCE_PASSWORD: ${POSTGRES_PASSWORD}
      SPRING_JPA_HIBERNATE_DDL_AUTO: update
      SPRING_JPA_SHOW_SQL: true
      JWT_SECRET: ${JWT_SECRET}
      JWT_EXPIRATION: ${JWT_EXPIRATION}
    ports:
      - "${SERVER_PORT}:8080"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - ${NETWORK_NAME}
    restart: unless-stopped

  # Frontend Angular
  frontend:
    build:
      context: ./perfecthost-ui
      dockerfile: Dockerfile
    container_name: ${FRONTEND_CONTAINER_NAME}
    environment:
      API_BASE_URL: ${API_BASE_URL}
    ports:
      - "${FRONTEND_PORT}:80"
    depends_on:
      - api
    networks:
      - ${NETWORK_NAME}
    restart: unless-stopped

# Volúmenes para persistir datos
volumes:
  postgres_data:

# Red personalizada
networks:
  perfecthost-network:
    name: ${NETWORK_NAME}
    driver: bridge
