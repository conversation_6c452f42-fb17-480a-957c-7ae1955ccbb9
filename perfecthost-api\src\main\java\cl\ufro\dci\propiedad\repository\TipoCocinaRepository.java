package cl.ufro.dci.propiedad.repository;

import cl.ufro.dci.propiedad.domain.TipoCocina;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * Repositorio para la entidad TipoCocina.
 * Proporciona métodos para realizar operaciones CRUD sobre los tipos de cocinas.
 */
@Repository
public interface TipoCocinaRepository extends JpaRepository<TipoCocina, Long> {
}
