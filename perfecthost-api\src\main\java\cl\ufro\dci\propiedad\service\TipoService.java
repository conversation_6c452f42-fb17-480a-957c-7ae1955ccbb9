package cl.ufro.dci.propiedad.service;

import cl.ufro.dci.propiedad.domain.tipoHospedaje;
import cl.ufro.dci.propiedad.dto.TipoDTO;
import cl.ufro.dci.propiedad.exception.ResourceNotFoundException;
import cl.ufro.dci.propiedad.repository.TipoRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Servicio que gestiona las operaciones relacionadas con los tipos de hospedaje.
 * Proporciona métodos para crear, leer, actualizar y eliminar tipos de hospedaje.
 */
@Service
public class TipoService {

    private final TipoRepository tipoRepository;

    @Autowired
    public TipoService(TipoRepository tipoRepository) {
        this.tipoRepository = tipoRepository;
    }

    public List<TipoDTO> getAllTipos() {
        return tipoRepository.findAll().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    public TipoDTO getTipoById(Long id) {
        tipoHospedaje tipo = tipoRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Tipo no encontrado con id: " + id));
        return convertToDTO(tipo);
    }

    public TipoDTO createTipo(TipoDTO tipoDTO) {
        tipoHospedaje tipo = new tipoHospedaje();
        tipo.setNombre(tipoDTO.getNombre());

        tipoHospedaje savedTipo = tipoRepository.save(tipo);
        return convertToDTO(savedTipo);
    }

    public TipoDTO updateTipo(Long id, TipoDTO tipoDTO) {
        tipoHospedaje tipo = tipoRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Tipo no encontrado con id: " + id));

        tipo.setNombre(tipoDTO.getNombre());

        tipoHospedaje updatedTipo = tipoRepository.save(tipo);
        return convertToDTO(updatedTipo);
    }

    public void deleteTipo(Long id) {
        if (!tipoRepository.existsById(id)) {
            throw new ResourceNotFoundException("Tipo no encontrado con id: " + id);
        }
        tipoRepository.deleteById(id);
    }

    private TipoDTO convertToDTO(tipoHospedaje tipo) {
        TipoDTO dto = new TipoDTO();
        dto.setTipoId(tipo.getTipoId());
        dto.setNombre(tipo.getNombre());
        return dto;
    }
}
