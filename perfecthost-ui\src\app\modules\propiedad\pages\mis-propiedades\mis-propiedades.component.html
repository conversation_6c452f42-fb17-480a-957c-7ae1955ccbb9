<div class="mis-propiedades-container">
  <div class="header">
    <h1>Mis Propiedades</h1>
    <button class="btn-agregar" routerLink="/propiedades/crear">
      <i class="bi bi-plus-lg"></i> Agregar Propiedad
    </button>
  </div>

  <div class="filtros">
    <div class="busqueda">
      <i class="bi bi-search"></i>
      <input type="text" placeholder="Buscar propiedades...">
    </div>

    <div class="filtro-grupo">
      <label>Ordenar por:</label>
      <select>
        <option value="reciente">Más reciente</option>
        <option value="antiguo">Más antiguo</option>
        <option value="precio-asc">Precio: menor a mayor</option>
        <option value="precio-desc">Precio: mayor a menor</option>
      </select>
    </div>
  </div>

  <div *ngIf="cargando" class="estado-mensaje">
    <div class="spinner"></div>
    <p>Cargando propiedades...</p>
  </div>

  <div *ngIf="error" class="estado-mensaje error">
    <i class="bi bi-exclamation-triangle"></i>
    <p>{{ error }}</p>
    <button (click)="cargarHospedajes()">Reintentar</button>
  </div>

  <div *ngIf="!cargando && !error && hospedajes.length === 0" class="estado-mensaje">
    <i class="bi bi-house"></i>
    <p>No tienes propiedades registradas</p>
    <button routerLink="/propiedades/crear">Crear mi primera propiedad</button>
  </div>

  <div *ngIf="!cargando && !error && hospedajes.length > 0" class="propiedades-grid">
    <app-hospedaje
      *ngFor="let hospedaje of hospedajes"
      [hospedaje]="hospedaje"
      (estadoCambiado)="onEstadoCambiado($event)"
      (eliminado)="onEliminado($event)">
    </app-hospedaje>
  </div>
</div>
