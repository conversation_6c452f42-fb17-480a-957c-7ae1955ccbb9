/* Estilos específicos para el layout de PerfectHost */

/* Asegurar que el layout ocupe toda la pantalla */
.h-screen {
  height: 100vh;
}

/* Flexbox para el layout principal */
.flex-1 {
  flex: 1;
}

/* Overflow para el contenido principal */
.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}

/* Transiciones suaves */
.transition-all {
  transition: all 0.3s ease-in-out;
}

/* Estilos específicos para PerfectHost */
.perfecthost-main {
  background: var(--background);
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .lg\:flex {
    display: flex;
  }
}
