package cl.ufro.dci.propiedad.domain;

import cl.ufro.dci.registro.domain.Usuario;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Entidad principal que representa un hospedaje o alojamiento en la plataforma.
 * Esta clase contiene toda la información relacionada con una propiedad disponible
 * para alojamiento, incluyendo sus características, ubicación, precios, y relaciones
 * con otras entidades como tipos de hospedaje, habitación, etc.
 */
@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Hospedaje {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long hosId;

    private String hosNombre;
    private int hosCapacidad;
    private int hosHabitaciones;
    private int hosBanios;
    private int hosPiscinas;
    private boolean hosWifi;
    private String politica;
    private String hosDescripcion;
    private String hosImagen;
    private int hosEstado;
    private float hosPrecio;

    // Dirección
    private String hosPais;
    private String hosRegion;
    private String hosCiudad;
    private String hosProvincia;
    private String hosComuna;
    private String hosCalle;
    private int hosNumero;
    private String hosPiso;
    private String hosDpto;
    private String hosCodigoPostal;
    private String hosLatitud;
    private String hosLongitud;
    private String hosDireccionCompleta;

    // Fechas
    private String hosFechaCreacion;
    private String hosFechaModificacion;

    // Relaciones
    @ManyToOne
    @JoinColumn(name = "tipo_id")
    private tipoHospedaje hosTipoHospedaje;

    @ManyToOne
    @JoinColumn(name = "tipo_habitacion_id")
    private TipoHabitacion hosTipoHabitacion;

    @ManyToOne
    @JoinColumn(name = "tipo_cocina_id")
    private TipoCocina hosTipoCocina;

    @ManyToOne
    @JoinColumn(name = "tipo_comodidad_id")
    private TipoComodidad hosComodidades;

    @ManyToOne
    @JoinColumn(name = "usuario_id")
    private Usuario usuario;
}
