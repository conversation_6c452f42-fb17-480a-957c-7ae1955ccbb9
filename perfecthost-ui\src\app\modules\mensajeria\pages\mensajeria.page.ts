import { Component } from '@angular/core';
import { HistorialComponent } from '../components/historial/historial.component';
import { ConversacionComponent } from '../components/conversacion/conversacion.component';

@Component({
  selector: 'app-mensajeria-page',
  standalone: true,
  imports: [HistorialComponent, ConversacionComponent],
  template: `
    <div class="bg-background min-h-screen">
      <div class="container mx-auto px-6 py-8">
        <!-- Header Section -->
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-foreground mb-2">Sistema de Mensajería</h1>
          <p class="text-muted-foreground">Comunícate con huéspedes y anfitriones de manera segura</p>
        </div>

        <!-- Chat Interface -->
        <div class="bg-card rounded-lg border border-border overflow-hidden" style="height: calc(100vh - 200px);">
          <div class="grid grid-cols-1 lg:grid-cols-3 h-full">
            <!-- Chat List -->
            <div class="lg:col-span-1 border-r border-border">
              <div class="p-4 border-b border-border">
                <h2 class="text-lg font-semibold text-foreground mb-3">Conversaciones</h2>
                <div class="relative">
                  <input
                    type="text"
                    placeholder="Buscar conversaciones..."
                    class="w-full pl-10 pr-4 py-2 border border-border rounded-lg bg-background text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                  <svg class="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                  </svg>
                </div>
              </div>
              <div class="overflow-y-auto" style="height: calc(100% - 120px);">
                <app-historial></app-historial>
              </div>
            </div>

            <!-- Chat Area -->
            <div class="lg:col-span-2 flex flex-col">
              <div class="p-4 border-b border-border">
                <div class="flex items-center space-x-3">
                  <div class="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                    <svg class="w-5 h-5 text-primary" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                    </svg>
                  </div>
                  <div>
                    <h3 class="font-semibold text-foreground">Selecciona una conversación</h3>
                    <p class="text-sm text-muted-foreground">Elige un chat para comenzar a conversar</p>
                  </div>
                </div>
              </div>
              <div class="flex-1 overflow-y-auto">
                <app-conversacion></app-conversacion>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .container {
      max-width: 1400px;
    }
  `]
})
export class MensajeriaPage {}
