<!-- PerfectHost Button -->
<div class="pt-4">
  <div class="mx-1 mb-2 flex items-center justify-between">
    <small class="text-muted-foreground/50 text-xs font-semibold">
      Aplicación
    </small>
  </div>
  <ul class="flex flex-col">
    <li>
      <div class="text-muted-foreground hover:text-foreground group flex grow items-center gap-4">
        <div class="text-muted-foreground/50">
          <svg-icon src="assets/icons/heroicons/outline/home.svg" [svgClass]="'h-5 w-5'"></svg-icon>
        </div>
        <div
          routerLink="/busqueda"
          class="text-muted-foreground hover:text-primary flex h-[36px] cursor-pointer items-center justify-start rounded-sm">
          <a
            routerLinkActive="text-primary"
            class="truncate text-xs font-semibold tracking-wide focus:outline-hidden">
            PerfectHost
          </a>
        </div>
      </div>
    </li>
  </ul>
</div>

<div class="pt-4" *ngFor="let menu of menuService.pagesMenu">
  <div class="mx-1 mb-2 flex items-center justify-between">
    <small class="text-muted-foreground/50 text-xs font-semibold">
      {{ menu.group }}
    </small>
  </div>
  <ul class="flex flex-col">
    <!-- List items -->
    <li *ngFor="let item of menu.items">
      <!-- Menu List -->
      <div
        (click)="toggleMenu(item)"
        class="text-muted-foreground hover:text-foreground group flex grow items-center gap-4">
        <!-- Icon -->
        <div [ngClass]="item.active && !menuService.showSideBar ? 'text-primary' : 'text-muted-foreground/50'">
          <svg-icon src="{{ item.icon }}" [svgClass]="'h-5 w-5'"> </svg-icon>
        </div>

        <!-- Condition -->
        <ng-container
          [ngTemplateOutlet]="item.children ? childMenu : parentMenu"
          [ngTemplateOutletContext]="{ item: item }">
        </ng-container>

        <!-- Workaround:: Enable routerLink -->
        <ng-template #parentMenu let-item="item">
          <div
            routerLink="{{ item.route }}"
            class="text-muted-foreground hover:text-primary flex h-[36px] cursor-pointer items-center justify-start rounded-sm">
            <a
              routerLinkActive="text-primary"
              class="truncate text-xs font-semibold tracking-wide focus:outline-hidden">
              {{ item.label }}
            </a>
          </div>
        </ng-template>

        <!-- Workaround:: Disable routerLink -->
        <ng-template #childMenu let-item="item">
          <div class="flex h-9 grow cursor-pointer items-center justify-start rounded-sm">
            <a
              class="text-muted-foreground group-hover:text-primary truncate text-xs font-semibold tracking-wide focus:outline-hidden">
              {{ item.label }}
            </a>
          </div>
        </ng-template>

        <!-- Arrow Icon -->
        <button
          *ngIf="item.children"
          [ngClass]="{ hidden: !menuService.showSideBar }"
          class="text-foreground/50 flex cursor-pointer items-center justify-end p-0 transition-all duration-500 ltr:right-0 rtl:left-0">
          @if(!item.expanded){
          <svg-icon src="assets/icons/heroicons/outline/plus.svg" svgClass="h-4 w-4"> </svg-icon>
          }@else {
          <svg-icon src="assets/icons/heroicons/outline/minus.svg" svgClass="h-4 w-4"> </svg-icon>
          }
        </button>
      </div>

      <!-- Submenu items -->
      <app-navbar-mobile-submenu [submenu]="item"></app-navbar-mobile-submenu>
    </li>
  </ul>
</div>
