version: '3.8'

# Configuración de desarrollo con hot-reload
# Usar con: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

services:
  perfecthost-api:
    build:
      context: ./perfecthost-api
      dockerfile: Dockerfile.dev
    volumes:
      - ./perfecthost-api/src:/app/src
      - ./perfecthost-api/target:/app/target
    environment:
      - SPRING_PROFILES_ACTIVE=dev
      - SPRING_DEVTOOLS_RESTART_ENABLED=true
    ports:
      - "8080:8080"
      - "35729:35729" # Puerto para LiveReload

  perfecthost-ui:
    build:
      context: ./perfecthost-ui
      dockerfile: Dockerfile.dev
    volumes:
      - ./perfecthost-ui/src:/app/src
      - ./perfecthost-ui/angular.json:/app/angular.json
      - ./perfecthost-ui/package.json:/app/package.json
      - ./perfecthost-ui/tsconfig.json:/app/tsconfig.json
      - ./perfecthost-ui/tailwind.config.js:/app/tailwind.config.js
      - ./perfecthost-ui/postcss.config.js:/app/postcss.config.js
    ports:
      - "4200:4200"
    command: npm start
    environment:
      - NODE_ENV=development
