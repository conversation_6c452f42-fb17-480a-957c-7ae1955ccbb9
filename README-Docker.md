# 🐳 PerfectHost - Configuración Docker

Este proyecto incluye una configuración completa de Docker para ejecutar tanto el frontend (Angular) como el backend (Spring Boot) en contenedores.

## 📋 Prerrequisitos

- Docker Desktop instalado y ejecutándose
- Docker Compose (incluido con Docker Desktop)

## 🚀 Inicio Rápido

### Opción 1: Usando Docker Compose directamente

```bash
# Construir y levantar todos los servicios
docker-compose up --build -d

# Ver logs
docker-compose logs -f

# Detener servicios
docker-compose down
```

### Opción 2: Usando scripts de ayuda

#### En Windows (PowerShell):
```powershell
# Cargar funciones
. .\docker-scripts.ps1

# Iniciar servicios
Start-PerfectHost

# Ver logs
Show-PerfectHostLogs
Show-PerfectHostLogs -Service perfecthost-api
Show-PerfectHostLogs -Service perfecthost-ui

# Detener servicios
Stop-PerfectHost
```

#### En Linux/Mac (Bash):
```bash
# Hacer ejecutable (solo la primera vez)
chmod +x docker-scripts.sh

# Iniciar servicios
./docker-scripts.sh start

# Ver logs
./docker-scripts.sh logs
./docker-scripts.sh logs api
./docker-scripts.sh logs ui

# Detener servicios
./docker-scripts.sh stop
```

## 🌐 Acceso a los Servicios

Una vez que los contenedores estén ejecutándose:

- **Frontend (Angular)**: http://localhost
- **Backend (Spring Boot)**: http://localhost:8081

## 📁 Estructura de Archivos Docker

```
perfecthost-app/
├── docker-compose.yml          # Configuración principal
├── docker-scripts.ps1          # Scripts para Windows
├── docker-scripts.sh           # Scripts para Linux/Mac
├── perfecthost-api/
│   ├── Dockerfile              # Imagen del backend
│   └── .dockerignore           # Archivos a ignorar
└── perfecthost-ui/
    ├── Dockerfile              # Imagen del frontend
    ├── nginx.conf              # Configuración de nginx
    └── .dockerignore           # Archivos a ignorar
```

## 🔧 Configuración Técnica

### Backend (Spring Boot)
- **Imagen base**: Maven 3.9.8 + Amazon Corretto 21 (construcción)
- **Imagen runtime**: OpenJDK 21
- **Puerto**: 8081
- **Construcción multi-etapa** para optimizar tamaño
- **Encoding**: UTF-8 configurado correctamente

### Frontend (Angular)
- **Imagen base**: Node 18 Alpine (construcción)
- **Imagen runtime**: Nginx Alpine
- **Puerto**: 80
- **Servidor web**: Nginx con configuración optimizada
- **Proxy**: Configurado para redirigir `/api/*` al backend

### Características de Nginx
- Compresión gzip habilitada
- Cache de archivos estáticos (1 año)
- Soporte para Angular routing (SPA)
- Headers de seguridad configurados
- Proxy reverso para el API

## 🛠️ Comandos Útiles

### Gestión de Contenedores
```bash
# Ver estado de los servicios
docker-compose ps

# Reconstruir sin cache
docker-compose build --no-cache

# Ver logs en tiempo real
docker-compose logs -f perfecthost-api
docker-compose logs -f perfecthost-ui

# Ejecutar comando en contenedor
docker-compose exec perfecthost-api bash
docker-compose exec perfecthost-ui sh
```

### Limpieza
```bash
# Detener y eliminar contenedores
docker-compose down

# Eliminar también volúmenes e imágenes
docker-compose down --rmi all --volumes --remove-orphans

# Limpiar sistema Docker completo (¡cuidado!)
docker system prune -a
```

## 🐛 Solución de Problemas

### El frontend no carga los estilos
- Los estilos se construyen durante el build de Docker
- Si hay problemas, reconstruir sin cache: `docker-compose build --no-cache perfecthost-ui`

### Error de conexión entre frontend y backend
- Verificar que ambos contenedores estén en la misma red
- Comprobar configuración de proxy en `nginx.conf`

### Problemas de permisos en Linux
```bash
# Dar permisos al script
chmod +x docker-scripts.sh

# Si hay problemas con Docker, agregar usuario al grupo docker
sudo usermod -aG docker $USER
# Luego cerrar sesión y volver a iniciar
```

### Puerto ya en uso
```bash
# Ver qué proceso usa el puerto
netstat -tulpn | grep :80
netstat -tulpn | grep :8080

# Cambiar puertos en docker-compose.yml si es necesario
```

## 📝 Notas Importantes

1. **Dependencias**: Las dependencias se instalan durante el build, no necesitas `npm install` local
2. **Desarrollo**: Para desarrollo activo, considera usar `docker-compose.override.yml` con volúmenes
3. **Producción**: Esta configuración está optimizada para producción con imágenes ligeras
4. **Base de datos**: Agregar servicio de PostgreSQL en `docker-compose.yml` si es necesario

## 🔄 Modo Desarrollo

Para desarrollo con hot-reload (cambios en tiempo real):

```bash
# Usar configuración de desarrollo
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --build

# Solo frontend en modo desarrollo
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up perfecthost-ui

# Solo backend en modo desarrollo
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up perfecthost-api
```

En modo desarrollo:
- **Frontend**: http://localhost:4200 (con hot-reload)
- **Backend**: http://localhost:8081 (con Spring DevTools)

## 🔄 Actualizaciones

Para actualizar después de cambios en el código:

```bash
# Reconstruir solo el servicio modificado
docker-compose build perfecthost-ui
docker-compose build perfecthost-api

# Reiniciar servicios
docker-compose up -d
```
