export interface Hospedaje {
  hosId: number
  hosNombre: string
  hosCapacidad: number
  hosHabitaciones: number
  hosBanios: number
  hosPiscinas: number
  hosWifi: boolean
  politica: string
  hosDescripcion: string
  hosImagen: string
  hosEstado: number
  hosPrecio: number
  hosPais: string
  hosRegion: string
  hosCiudad: string
  hosProvincia: string
  hosComuna: string
  hosCalle: string
  hosNumero: number
  hosPiso: string
  hosDpto: string
  hosCodigoPostal: string
  hosLatitud: string
  hosLongitud: string
  hosDireccionCompleta: string
  hosFechaCreacion: string
  hosFechaModificacion: string
  hosTipoHospedajeId: number
  hosTipoHospedajeNombre: string
  hosTipoHabitacionId: number
  hosTipoHabitacionNombre: string
  hosTipoCocinaId: number
  hosTipoCocianNombre: string
  hosComodidadesId: number
  hosComodidadesNombre: string
  usuarioId: number
}

export interface Tipo {
  tipoId: number
  nombre: string
}

export interface Catalogo {
  id: number
  nombre: string
}

export interface Pais {
  id: number
  nombre: string
  codigo: string
}

export enum EstadoHospedaje {
  ACTIVO = 1,
  INACTIVO = 2,
  EN_REVISION = 3,
  RECHAZADO = 4,
}
