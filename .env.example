# Ejemplo de variables de entorno para GitLab CI/CD
# Copia este contenido y configúralo en GitLab > Settings > CI/CD > Variables

# === VARIABLES DE GITLAB REGISTRY ===
********************.gitlab.com
CI_REGISTRY_IMAGE=registry.gitlab.com/tu-usuario/perfecthost-app
GITLAB_USERNAME=tu-usuario-gitlab
GITLAB_TOKEN=tu-token-de-acceso

# === CONFIGURACIÓN DEL VPS ===
VPS_HOST=tu-vps-ip-o-dominio
VPS_USER=tu-usuario-vps
VPS_SSH_PRIVATE_KEY=-----BEGIN OPENSSH PRIVATE KEY-----
# Tu clave SSH privada aquí (en una sola línea o con \n)
# -----END OPENSSH PRIVATE KEY-----

# === CONFIGURACIÓN DE LA APLICACIÓN ===
# Puertos de la aplicación
UI_PORT=4200
API_PORT=8080

# Perfil de Spring Boot
SPRING_PROFILE=production

# === BASE DE DATOS ===
DATABASE_URL=********************************************
DATABASE_USERNAME=perfecthost_user
DATABASE_PASSWORD=tu-password-seguro

# === SEGURIDAD ===
JWT_SECRET=tu-jwt-secret-muy-seguro-de-al-menos-256-bits
CORS_ALLOWED_ORIGINS=http://tu-dominio.com,https://tu-dominio.com

# === URLs DE LA APLICACIÓN ===
API_URL=http://tu-vps-ip:8080

# === CONFIGURACIÓN ADICIONAL ===
# Configuraciones específicas de tu aplicación
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=tu-password-email

# Configuración de logging
LOGGING_LEVEL_ROOT=INFO
LOGGING_LEVEL_COM_PERFECTHOST=DEBUG
