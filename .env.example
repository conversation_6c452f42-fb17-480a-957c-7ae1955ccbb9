# Variables de entorno para docker-compose.prod.yml
# Este archivo se crea automáticamente en el VPS durante el despliegue

# Configuración de puertos
UI_PORT=4200
API_PORT=8080

# Configuración de Spring Boot
SPRING_PROFILE=docker

# Base de datos
DATABASE_URL=********************************************
DATABASE_USERNAME=perfecthost_user
DATABASE_PASSWORD=perfecthost_password
# Seguridad
JWT_SECRET=eysjkLKSIFKk29JKSDL-@cks
CORS_ALLOWED_ORIGINS=http://200.:4200

# URL del API para el frontend
API_URL=http://tu-vps-ip:8080

# Credenciales de GitLab (para login al registry en el VPS)
GITLAB_USERNAME=tu-usuario-gitlab
GITLAB_TOKEN=tu-token-de-acceso
