# Variables de entorno para GitLab CI/CD y Despliegue
# Configura estas variables en GitLab > Settings > CI/CD > Variables

# === VARIABLES PARA GITLAB CI/CD ===
# Variables del VPS (configurar en GitLab)
*********************dominio
VPS_USER=tu-usuario-vps
SSH_SERVER_KEY_KEY=-----BEGIN OPENSSH PRIVATE KEY-----
# Tu clave SSH privada completa aquí
# -----END OPENSSH PRIVATE KEY-----

# === VARIABLE PRODUCTION_ENV ===
# Esta variable debe contener todo el bloque siguiente:

# Credenciales de GitLab (para login al registry)
GITLAB_USERNAME=tu-usuario-gitlab
GITLAB_TOKEN=tu-token-de-acceso

# Configuración de puertos
UI_PORT=4200
API_PORT=8080

# Configuración de Spring Boot
SPRING_PROFILE=production

# Base de datos
DATABASE_URL=********************************************
DATABASE_USERNAME=perfecthost_user
DATABASE_PASSWORD=tu-password-seguro

# Seguridad
JWT_SECRET=tu-jwt-secret-muy-seguro-de-al-menos-32-caracteres
CORS_ALLOWED_ORIGINS=http://tu-vps-ip:4200

# URL del API para el frontend
API_URL=http://tu-vps-ip:8080
