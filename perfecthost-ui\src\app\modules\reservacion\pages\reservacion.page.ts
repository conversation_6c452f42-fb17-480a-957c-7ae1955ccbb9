import { Component } from '@angular/core';
import { ReservaFormComponent } from '../components/reserva-form/reserva-form.component';
import { StatCardComponent } from '../../../shared/components/stat-card/stat-card.component';
import { ContentCardComponent } from '../../../shared/components/content-card/content-card.component';
import { PageHeaderComponent } from '../../../shared/components/page-header/page-header.component';

@Component({
  selector: 'app-reservacion-page',
  standalone: true,
  imports: [ReservaFormComponent, StatCardComponent, ContentCardComponent, PageHeaderComponent],
  template: `
    <div class="bg-background min-h-screen">
      <div class="container mx-auto px-6 py-8">
        <!-- Header Section -->
        <app-page-header
          title="Gestión de Reservas"
          description="Administra todas tus reservas y solicitudes de hospedaje">
        </app-page-header>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <app-stat-card
            label="Reservas Activas"
            value="12"
            variant="primary"
            iconPath="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
          </app-stat-card>

          <app-stat-card
            label="Pendientes"
            value="5"
            variant="warning"
            iconPath="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z">
          </app-stat-card>

          <app-stat-card
            label="Completadas"
            value="48"
            variant="success"
            iconPath="M5 13l4 4L19 7">
          </app-stat-card>

          <app-stat-card
            label="Ingresos"
            value="$2,450"
            variant="info"
            iconPath="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
          </app-stat-card>
        </div>

        <!-- Content Card -->
        <app-content-card title="Reservas Recientes">
          <div slot="actions">
            <button class="bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded-lg font-medium transition-colors">
              Nueva Reserva
            </button>
            <button class="border border-border text-foreground hover:bg-card px-4 py-2 rounded-lg font-medium transition-colors">
              Filtros
            </button>
          </div>
          <app-reserva-form></app-reserva-form>
        </app-content-card>
      </div>
    </div>
  `,
  styles: [`
    .container {
      max-width: 1400px;
    }
  `]
})
export class ReservacionPage {}
