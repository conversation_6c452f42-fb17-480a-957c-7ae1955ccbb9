spring.application.name=ufro.dci

# Perfil activo
spring.profiles.active=development

# Configuración de base de datos H2 para desarrollo
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

# Habilitar consola H2
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# Configuración JPA
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true

# JWT CONFIGURATION
jwt.secret=1234567890
jwt.expiration=3600000

# Datos de prueba para bases de datos H2
spring.jpa.defer-datasource-initialization=true
spring.sql.init.mode=always