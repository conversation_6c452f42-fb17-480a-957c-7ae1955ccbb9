import { Component } from '@angular/core';
import { ValoracionComponent } from '../components/valoracion/valoracion.component';
import { StatCardComponent } from '../../../shared/components/stat-card/stat-card.component';
import { ContentCardComponent } from '../../../shared/components/content-card/content-card.component';
import { PageHeaderComponent } from '../../../shared/components/page-header/page-header.component';

@Component({
  selector: 'app-evaluacion-page',
  standalone: true,
  imports: [ValoracionComponent, StatCardComponent, ContentCardComponent, PageHeaderComponent],
  template: `
    <div class="bg-background min-h-screen">
      <div class="container mx-auto px-6 py-8">
        <!-- Header Section -->
        <app-page-header
          title="Sistema de Evaluaciones"
          description="Gestiona las calificaciones y reseñas de tus propiedades">
        </app-page-header>

        <!-- Stats Overview -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <app-stat-card
            label="Calificación Promedio"
            value="4.8"
            subtitle="★★★★★"
            variant="primary"
            iconPath="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z">
          </app-stat-card>

          <app-stat-card
            label="Total Reseñas"
            value="127"
            variant="info"
            iconPath="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z">
          </app-stat-card>

          <app-stat-card
            label="Satisfacción"
            value="98%"
            variant="success"
            iconPath="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
          </app-stat-card>
        </div>

        <!-- Content Card -->
        <app-content-card title="Evaluaciones Recientes">
          <div slot="actions">
            <button class="border border-border text-foreground hover:bg-card px-4 py-2 rounded-lg font-medium transition-colors">
              Ver Todas
            </button>
          </div>
          <app-valoracion></app-valoracion>
        </app-content-card>
      </div>
    </div>
  `,
  styles: [`
    .container {
      max-width: 1400px;
    }
  `]
})
export class EvaluacionPage {}
