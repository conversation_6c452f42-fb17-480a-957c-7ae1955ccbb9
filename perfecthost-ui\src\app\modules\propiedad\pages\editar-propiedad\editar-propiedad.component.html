<div class="editar-propiedad-container">
  <div class="header">
    <h1>Editar Propiedad</h1>
    <button class="btn-volver" routerLink="/propiedades">
      <i class="bi bi-arrow-left"></i> Volver a Mis Propiedades
    </button>
  </div>

  <div *ngIf="error" class="alerta-error">
    <i class="bi bi-exclamation-triangle"></i>
    <p>{{ error }}</p>
  </div>

  <div *ngIf="cargando" class="estado-mensaje">
    <div class="spinner"></div>
    <p>Cargando datos de la propiedad...</p>
  </div>

  <form *ngIf="!cargando && hospedaje" [formGroup]="propiedadForm" (ngSubmit)="onSubmit()" class="propiedad-form">
    <!-- Sección: Información Básica -->
    <div class="form-section">
      <h2>Información Básica</h2>

      <div class="form-group">
        <label for="hosNombre">Nombre de la propiedad *</label>
        <input
          type="text"
          id="hosNombre"
          formControlName="hosNombre"
          placeholder="Ej: Casa de playa con vista al mar"
        >
        <div *ngIf="campoInvalido('hosNombre')" class="error-mensaje">
          {{ obtenerErrorMensaje('hosNombre') }}
        </div>
      </div>

      <div class="form-group">
        <label for="hosDescripcion">Descripción *</label>
        <textarea
          id="hosDescripcion"
          formControlName="hosDescripcion"
          rows="4"
          placeholder="Describe tu propiedad detalladamente..."
        ></textarea>
        <div *ngIf="campoInvalido('hosDescripcion')" class="error-mensaje">
          {{ obtenerErrorMensaje('hosDescripcion') }}
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="hosTipoHospedajeId">Tipo de propiedad *</label>
          <select id="hosTipoHospedajeId" formControlName="hosTipoHospedajeId">
            <option [ngValue]="null" disabled>Selecciona un tipo</option>
            <option *ngFor="let tipo of tipos" [value]="tipo.tipoId">{{ tipo.nombre }}</option>
          </select>
          <div *ngIf="campoInvalido('hosTipoHospedajeId')" class="error-mensaje">
            {{ obtenerErrorMensaje('hosTipoHospedajeId') }}
          </div>
        </div>

        <div class="form-group">
          <label for="hosTipoHabitacionId">Tipo de habitación *</label>
          <select id="hosTipoHabitacionId" formControlName="hosTipoHabitacionId">
            <option [ngValue]="null" disabled>Selecciona un tipo</option>
            <option *ngFor="let tipo of tiposHabitacion" [value]="tipo.id">{{ tipo.nombre }}</option>
          </select>
          <div *ngIf="campoInvalido('hosTipoHabitacionId')" class="error-mensaje">
            {{ obtenerErrorMensaje('hosTipoHabitacionId') }}
          </div>
        </div>
      </div>
    </div>

    <!-- Sección: Capacidad y Características -->
    <div class="form-section">
      <h2>Capacidad y Características</h2>

      <div class="form-row">
        <div class="form-group">
          <label for="hosCapacidad">Capacidad (personas) *</label>
          <input type="number" id="hosCapacidad" formControlName="hosCapacidad" min="1" max="20">
          <div *ngIf="campoInvalido('hosCapacidad')" class="error-mensaje">
            {{ obtenerErrorMensaje('hosCapacidad') }}
          </div>
        </div>

        <div class="form-group">
          <label for="hosHabitaciones">Habitaciones *</label>
          <input type="number" id="hosHabitaciones" formControlName="hosHabitaciones" min="1" max="10">
          <div *ngIf="campoInvalido('hosHabitaciones')" class="error-mensaje">
            {{ obtenerErrorMensaje('hosHabitaciones') }}
          </div>
        </div>

        <div class="form-group">
          <label for="hosBanios">Baños *</label>
          <input type="number" id="hosBanios" formControlName="hosBanios" min="1" max="10">
          <div *ngIf="campoInvalido('hosBanios')" class="error-mensaje">
            {{ obtenerErrorMensaje('hosBanios') }}
          </div>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="hosPiscinas">Piscinas</label>
          <input type="number" id="hosPiscinas" formControlName="hosPiscinas" min="0" max="5">
          <div *ngIf="campoInvalido('hosPiscinas')" class="error-mensaje">
            {{ obtenerErrorMensaje('hosPiscinas') }}
          </div>
        </div>

        <div class="form-group">
          <label for="hosTipoCocinaId">Tipo de cocina *</label>
          <select id="hosTipoCocinaId" formControlName="hosTipoCocinaId">
            <option [ngValue]="null" disabled>Selecciona un tipo</option>
            <option *ngFor="let tipo of tiposCocina" [value]="tipo.id">{{ tipo.nombre }}</option>
          </select>
          <div *ngIf="campoInvalido('hosTipoCocinaId')" class="error-mensaje">
            {{ obtenerErrorMensaje('hosTipoCocinaId') }}
          </div>
        </div>

        <div class="form-group">
          <label for="hosComodidadesId">Nivel de comodidades *</label>
          <select id="hosComodidadesId" formControlName="hosComodidadesId">
            <option [ngValue]="null" disabled>Selecciona un nivel</option>
            <option *ngFor="let tipo of tiposComodidad" [value]="tipo.id">{{ tipo.nombre }}</option>
          </select>
          <div *ngIf="campoInvalido('hosComodidadesId')" class="error-mensaje">
            {{ obtenerErrorMensaje('hosComodidadesId') }}
          </div>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="hosPrecio">Precio por noche (USD) *</label>
          <input type="number" id="hosPrecio" formControlName="hosPrecio" min="1">
          <div *ngIf="campoInvalido('hosPrecio')" class="error-mensaje">
            {{ obtenerErrorMensaje('hosPrecio') }}
          </div>
        </div>

        <div class="form-group checkbox-group">
          <label class="checkbox-label">
            <input type="checkbox" formControlName="hosWifi">
            <span>WiFi disponible</span>
          </label>
        </div>
      </div>
    </div>

    <!-- Sección: Políticas -->
    <div class="form-section">
      <h2>Políticas de la Propiedad</h2>

      <div class="form-group">
        <label for="politica">Políticas y reglas *</label>
        <textarea
          id="politica"
          formControlName="politica"
          rows="4"
          placeholder="Describe las reglas y políticas de tu propiedad..."
        ></textarea>
        <div *ngIf="campoInvalido('politica')" class="error-mensaje">
          {{ obtenerErrorMensaje('politica') }}
        </div>
      </div>
    </div>

    <!-- Sección: Imagen -->
    <div class="form-section">
      <h2>Imagen Principal</h2>

      <div class="form-group">
        <label for="hosImagen">URL de la imagen *</label>
        <input
          type="text"
          id="hosImagen"
          formControlName="hosImagen"
          placeholder="https://ejemplo.com/imagen.jpg"
        >
        <div *ngIf="campoInvalido('hosImagen')" class="error-mensaje">
          {{ obtenerErrorMensaje('hosImagen') }}
        </div>
      </div>

      <div *ngIf="imagenPreview" class="imagen-preview">
        <h3>Vista previa:</h3>
        <img [src]="imagenPreview" alt="Vista previa de la imagen">
      </div>
    </div>

    <!-- Sección: Ubicación -->
    <div class="form-section">
      <h2>Ubicación</h2>

      <div class="form-row">
        <div class="form-group">
          <label for="hosPais">País *</label>
          <select id="hosPais" formControlName="hosPais">
            <option value="" disabled>Selecciona un país</option>
            <option *ngFor="let pais of paises" [value]="pais.nombre">{{ pais.nombre }}</option>
          </select>
          <div *ngIf="campoInvalido('hosPais')" class="error-mensaje">
            {{ obtenerErrorMensaje('hosPais') }}
          </div>
        </div>

        <div class="form-group">
          <label for="hosRegion">Región/Estado *</label>
          <input type="text" id="hosRegion" formControlName="hosRegion">
          <div *ngIf="campoInvalido('hosRegion')" class="error-mensaje">
            {{ obtenerErrorMensaje('hosRegion') }}
          </div>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="hosCiudad">Ciudad *</label>
          <input type="text" id="hosCiudad" formControlName="hosCiudad">
          <div *ngIf="campoInvalido('hosCiudad')" class="error-mensaje">
            {{ obtenerErrorMensaje('hosCiudad') }}
          </div>
        </div>

        <div class="form-group">
          <label for="hosProvincia">Provincia</label>
          <input type="text" id="hosProvincia" formControlName="hosProvincia">
        </div>

        <div class="form-group">
          <label for="hosComuna">Comuna/Distrito *</label>
          <input type="text" id="hosComuna" formControlName="hosComuna">
          <div *ngIf="campoInvalido('hosComuna')" class="error-mensaje">
            {{ obtenerErrorMensaje('hosComuna') }}
          </div>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="hosCalle">Calle *</label>
          <input type="text" id="hosCalle" formControlName="hosCalle">
          <div *ngIf="campoInvalido('hosCalle')" class="error-mensaje">
            {{ obtenerErrorMensaje('hosCalle') }}
          </div>
        </div>

        <div class="form-group">
          <label for="hosNumero">Número *</label>
          <input type="number" id="hosNumero" formControlName="hosNumero" min="1">
          <div *ngIf="campoInvalido('hosNumero')" class="error-mensaje">
            {{ obtenerErrorMensaje('hosNumero') }}
          </div>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="hosPiso">Piso</label>
          <input type="text" id="hosPiso" formControlName="hosPiso">
        </div>

        <div class="form-group">
          <label for="hosDpto">Departamento/Oficina</label>
          <input type="text" id="hosDpto" formControlName="hosDpto">
        </div>

        <div class="form-group">
          <label for="hosCodigoPostal">Código Postal</label>
          <input type="text" id="hosCodigoPostal" formControlName="hosCodigoPostal">
        </div>
      </div>
    </div>

    <!-- Botones de acción -->
    <div class="form-actions">
      <button type="button" class="btn-cancelar" routerLink="/propiedades">Cancelar</button>
      <button
        type="submit"
        class="btn-guardar"
        [disabled]="enviando || propiedadForm.invalid"
      >
        <span *ngIf="enviando" class="spinner-sm"></span>
        {{ enviando ? 'Guardando...' : 'Actualizar Propiedad' }}
      </button>
    </div>
  </form>
</div>
