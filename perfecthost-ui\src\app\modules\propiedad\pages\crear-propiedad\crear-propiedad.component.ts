import {Component, inject, OnInit} from '@angular/core';
import { HospedajeService } from '../../services/hospedaje.service';
import {FormBuilder, FormGroup, ReactiveFormsModule, Validators} from '@angular/forms';
import {Router, RouterLink} from '@angular/router';
import {Catalogo, Pais, Tipo} from '../../models/hospedaje.model';
import {finalize, forkJoin} from 'rxjs';
import {NgForOf, NgIf} from '@angular/common';

@Component({
  selector: 'app-crear-propiedad',
  imports: [
    RouterLink,
    NgIf,
    ReactiveFormsModule,
    NgForOf
  ],
  templateUrl: './crear-propiedad.component.html',
  styleUrl: './crear-propiedad.component.css'
})
export class CrearPropiedadComponent implements OnInit {
  private fb = inject(FormBuilder)
  private hospedajeService = inject(HospedajeService)
  private router = inject(Router)

  propiedadForm!: FormGroup
  tipos: Tipo[] = []
  tiposHabitacion: Catalogo[] = []
  tiposCocina: Catalogo[] = []
  tiposComodidad: Catalogo[] = []
  paises: Pais[] = []

  cargandoCatalogos = true
  enviando = false
  error: string | null = null

  // Para la vista previa de imagen
  imagenPreview: string | null = null

  ngOnInit(): void {
    this.crearFormulario()
    this.cargarCatalogos()

    // Suscribirse a cambios en el campo de imagen para mostrar vista previa
    this.propiedadForm.get("hosImagen")?.valueChanges.subscribe((url) => {
      if (url && url.trim() !== "") {
        this.imagenPreview = url
      } else {
        this.imagenPreview = null
      }
    })
  }

  crearFormulario(): void {
    this.propiedadForm = this.fb.group({
      // Información básica
      hosNombre: ["", [Validators.required, Validators.minLength(5), Validators.maxLength(100)]],
      hosDescripcion: ["", [Validators.required, Validators.minLength(20)]],
      hosTipoHospedajeId: [null, Validators.required],
      hosTipoHabitacionId: [null, Validators.required],

      // Capacidad y características
      hosCapacidad: [1, [Validators.required, Validators.min(1), Validators.max(20)]],
      hosHabitaciones: [1, [Validators.required, Validators.min(1), Validators.max(10)]],
      hosBanios: [1, [Validators.required, Validators.min(1), Validators.max(10)]],
      hosPiscinas: [0, [Validators.required, Validators.min(0), Validators.max(5)]],
      hosTipoCocinaId: [null, Validators.required],
      hosComodidadesId: [null, Validators.required],
      hosPrecio: [0, [Validators.required, Validators.min(1)]],
      hosWifi: [false],

      // Políticas
      politica: ["", Validators.required],

      // Imagen
      hosImagen: ["", Validators.required],

      // Ubicación
      hosPais: ["", Validators.required],
      hosRegion: ["", Validators.required],
      hosCiudad: ["", Validators.required],
      hosProvincia: [""],
      hosComuna: ["", Validators.required],
      hosCalle: ["", Validators.required],
      hosNumero: [null, [Validators.required, Validators.min(1)]],
      hosPiso: [""],
      hosDpto: [""],
      hosCodigoPostal: [""],
      hosLatitud: ["0"],
      hosLongitud: ["0"],

      // Usuario (en una aplicación real, esto vendría de un servicio de autenticación)
      usuarioId: [1],
    })
  }

  cargarCatalogos(): void {
    this.cargandoCatalogos = true
    this.error = null

    // Cargar todos los catálogos en paralelo
    forkJoin({
      tipos: this.hospedajeService.getTipos(),
      tiposHabitacion: this.hospedajeService.getTiposHabitacion(),
      tiposCocina: this.hospedajeService.getTiposCocina(),
      tiposComodidad: this.hospedajeService.getTiposComodidad(),
      paises: this.hospedajeService.getPaises(),
    })
      .pipe(finalize(() => (this.cargandoCatalogos = false)))
      .subscribe({
        next: (result) => {
          this.tipos = result.tipos
          this.tiposHabitacion = result.tiposHabitacion
          this.tiposCocina = result.tiposCocina
          this.tiposComodidad = result.tiposComodidad
          this.paises = result.paises

          // Establecer valores por defecto si hay datos
          if (this.tipos.length > 0) {
            this.propiedadForm.patchValue({ hosTipoHospedajeId: this.tipos[0].tipoId })
          }
          if (this.tiposHabitacion.length > 0) {
            this.propiedadForm.patchValue({ hosTipoHabitacionId: this.tiposHabitacion[0].id })
          }
          if (this.tiposCocina.length > 0) {
            this.propiedadForm.patchValue({ hosTipoCocinaId: this.tiposCocina[0].id })
          }
          if (this.tiposComodidad.length > 0) {
            this.propiedadForm.patchValue({ hosComodidadesId: this.tiposComodidad[0].id })
          }
        },
        error: (error) => {
          console.error("Error al cargar catálogos:", error)
          this.error = "No se pudieron cargar los datos necesarios. Por favor, recargue la página."
        },
      })
  }

  onSubmit(): void {
    if (this.propiedadForm.invalid) {
      // Marcar todos los campos como tocados para mostrar errores
      Object.keys(this.propiedadForm.controls).forEach((key) => {
        const control = this.propiedadForm.get(key)
        control?.markAsTouched()
      })
      return
    }

    this.enviando = true
    this.error = null

    this.hospedajeService
      .crearHospedaje(this.propiedadForm.value)
      .pipe(finalize(() => (this.enviando = false)))
      .subscribe({
        next: () => {
          // Redirigir a la lista de propiedades con un mensaje de éxito
          this.router.navigate(["/propiedades"], {
            state: { mensaje: "Propiedad creada exitosamente" },
          })
        },
        error: (err) => {
          this.error = err.message || "Error al crear la propiedad. Por favor, intente nuevamente."
          window.scrollTo(0, 0) // Desplazar al inicio para mostrar el error
        },
      })
  }

  // Helpers para validación
  campoInvalido(campo: string): boolean {
    const control = this.propiedadForm.get(campo)
    return !!control && control.invalid && (control.dirty || control.touched)
  }

  obtenerErrorMensaje(campo: string): string {
    const control = this.propiedadForm.get(campo)

    if (!control) return ""

    if (control.hasError("required")) {
      return "Este campo es obligatorio"
    }

    if (control.hasError("minlength")) {
      const minLength = control.getError("minlength").requiredLength
      return `Debe tener al menos ${minLength} caracteres`
    }

    if (control.hasError("maxlength")) {
      const maxLength = control.getError("maxlength").requiredLength
      return `No puede tener más de ${maxLength} caracteres`
    }

    if (control.hasError("min")) {
      const min = control.getError("min").min
      return `El valor mínimo es ${min}`
    }

    if (control.hasError("max")) {
      const max = control.getError("max").max
      return `El valor máximo es ${max}`
    }

    return "Campo inválido"
  }
}
