import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Hospedaje } from '../../propiedad/models/hospedaje.model';
import { ParametrosBusqueda } from '../model/parametros-busqueda';

@Injectable({
  providedIn: 'root'
})
export class BusquedaService {
  private apiBusqueda: string = 'http://localhost:8080/api/busqueda/hospedajes';

  private http = inject(HttpClient);

  obtenerHospedajesTotales(): Observable<Hospedaje[]>{
    return this.http.get<Hospedaje[]>(this.apiBusqueda);
  }

  obtenerHospedajes(params: ParametrosBusqueda): Observable<Hospedaje[]> {
    let queryParams = new HttpParams()
      .set('ubicacion', params.ubicacion || '')
      .set('fechaLlegada', params.fechaLlegada || '')
      .set('fechaSalida', params.fechaSalida || '')
      .set('cantidadAdultos', params.cantidadAdultos.toString())
      .set('cantidadMenores', params.cantidadMenores.toString())
      .set('cantidadMascotas', params.cantidadMascotas.toString());

    return this.http.get<Hospedaje[]>(this.apiBusqueda, { params: queryParams });
  }
}
