#!/bin/bash

# Scripts de Docker para PerfectHost
# Ejecutar desde la raíz del proyecto

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Función para construir y levantar los servicios
start_perfecthost() {
    echo -e "${GREEN}🚀 Iniciando PerfectHost...${NC}"
    docker-compose up --build -d
    echo -e "${GREEN}✅ Servicios iniciados!${NC}"
    echo -e "${CYAN}🌐 Frontend: http://localhost${NC}"
    echo -e "${CYAN}🔧 Backend: http://localhost:8080${NC}"
}

# Función para detener los servicios
stop_perfecthost() {
    echo -e "${YELLOW}🛑 Deteniendo PerfectHost...${NC}"
    docker-compose down
    echo -e "${GREEN}✅ Servicios detenidos!${NC}"
}

# Función para ver logs
show_logs() {
    local service=$1
    if [ -z "$service" ]; then
        docker-compose logs -f
    else
        docker-compose logs -f "$service"
    fi
}

# Función para reconstruir completamente
rebuild_perfecthost() {
    echo -e "${BLUE}🔄 Reconstruyendo PerfectHost...${NC}"
    docker-compose down
    docker-compose build --no-cache
    docker-compose up -d
    echo -e "${GREEN}✅ Reconstrucción completa!${NC}"
}

# Función para limpiar contenedores e imágenes
clean_perfecthost() {
    echo -e "${RED}🧹 Limpiando contenedores e imágenes...${NC}"
    docker-compose down --rmi all --volumes --remove-orphans
    echo -e "${GREEN}✅ Limpieza completa!${NC}"
}

# Función para mostrar estado de los servicios
status_perfecthost() {
    echo -e "${CYAN}📊 Estado de los servicios:${NC}"
    docker-compose ps
}

# Función para mostrar ayuda
show_help() {
    echo -e "${MAGENTA}🐳 Scripts de Docker para PerfectHost${NC}"
    echo ""
    echo -e "${WHITE}Comandos disponibles:${NC}"
    echo -e "${GREEN}  ./docker-scripts.sh start      - Construir y levantar servicios${NC}"
    echo -e "${YELLOW}  ./docker-scripts.sh stop       - Detener servicios${NC}"
    echo -e "${CYAN}  ./docker-scripts.sh logs       - Ver logs de todos los servicios${NC}"
    echo -e "${CYAN}  ./docker-scripts.sh logs api   - Ver logs del backend${NC}"
    echo -e "${CYAN}  ./docker-scripts.sh logs ui    - Ver logs del frontend${NC}"
    echo -e "${BLUE}  ./docker-scripts.sh rebuild    - Reconstruir completamente${NC}"
    echo -e "${RED}  ./docker-scripts.sh clean      - Limpiar todo${NC}"
    echo -e "${CYAN}  ./docker-scripts.sh status     - Ver estado de servicios${NC}"
    echo ""
    echo -e "${WHITE}Ejemplos:${NC}"
    echo "  ./docker-scripts.sh start"
    echo "  ./docker-scripts.sh logs perfecthost-api"
    echo "  ./docker-scripts.sh logs perfecthost-ui"
}

# Procesar argumentos
case "$1" in
    start)
        start_perfecthost
        ;;
    stop)
        stop_perfecthost
        ;;
    logs)
        if [ "$2" = "api" ]; then
            show_logs "perfecthost-api"
        elif [ "$2" = "ui" ]; then
            show_logs "perfecthost-ui"
        else
            show_logs "$2"
        fi
        ;;
    rebuild)
        rebuild_perfecthost
        ;;
    clean)
        clean_perfecthost
        ;;
    status)
        status_perfecthost
        ;;
    *)
        show_help
        ;;
esac
