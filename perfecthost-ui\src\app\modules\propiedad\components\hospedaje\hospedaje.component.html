<div class="hospedaje-card">
  <div class="hospedaje-imagen">
    <img [src]="hospedaje.hosImagen || 'assets/images/placeholder-property.jpg'" [alt]="hospedaje.hosNombre">
    <div class="estado-badge" [ngClass]="getEstadoClase()">
      {{ estadoTexto }}
    </div>
  </div>

  <div class="hospedaje-info">
    <h3>{{ hospedaje.hosNombre }}</h3>
    <p class="tipo">{{ hospedaje.hosTipoHospedajeNombre || 'Tipo desconocido' }}</p>
    <p class="ubicacion">{{ hospedaje.hosCiudad }}, {{ hospedaje.hosPais }}</p>
    <p class="precio">{{ hospedaje.hosPrecio | currency:'USD':'symbol':'1.0-0' }} por noche</p>

    <div class="detalles">
      <div class="detalle">
        <i class="bi bi-people"></i>
        <span>{{ hospedaje.hosCapacidad }} huéspedes</span>
      </div>
      <div class="detalle">
        <i class="bi bi-door-closed"></i>
        <span>{{ hospedaje.hosHabitaciones }} habitaciones</span>
      </div>
      <div class="detalle">
        <i class="bi bi-droplet"></i>
        <span>{{ hospedaje.hosBanios }} baños</span>
      </div>
      <div class="detalle" *ngIf="hospedaje.hosWifi">
        <i class="bi bi-wifi"></i>
        <span>WiFi</span>
      </div>
    </div>
  </div>

  <div class="hospedaje-acciones">
    <button class="btn-editar" [routerLink]="['/propiedades/editar', hospedaje.hosId]">
      <i class="bi bi-pencil"></i> Editar
    </button>

    <div class="dropdown">
      <button class="btn-estado">
        <i class="bi bi-toggle-on"></i> Estado <i class="bi bi-chevron-down"></i>
      </button>
      <div class="dropdown-menu">
        <button (click)="cambiarEstado(estadoHospedaje.ACTIVO)"
                [disabled]="hospedaje.hosEstado === estadoHospedaje.ACTIVO">Activo</button>
        <button (click)="cambiarEstado(estadoHospedaje.INACTIVO)"
                [disabled]="hospedaje.hosEstado === estadoHospedaje.INACTIVO">Inactivo</button>
      </div>
    </div>

    <button class="btn-eliminar" (click)="eliminar()">
      <i class="bi bi-trash"></i> Eliminar
    </button>
  </div>
</div>
