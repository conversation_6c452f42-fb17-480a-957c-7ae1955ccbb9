import { Component, Input, Output, EventEmitter } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { AngularSvgIconModule } from 'angular-svg-icon';

@Component({
  selector: 'app-huespedes-modal',
  imports: [CommonModule, ReactiveFormsModule, AngularSvgIconModule],
  templateUrl: './huespedes-modal.component.html',
  styleUrl: './huespedes-modal.component.css'
})
export class HuespedesModalComponent {
  @Input() isOpen = false;
  @Input() title = 'Cantidad de huéspedes';
  @Input() form!: FormGroup;

  @Output() closed = new EventEmitter<void>();

  close() {
    this.closed.emit();
  }
}
