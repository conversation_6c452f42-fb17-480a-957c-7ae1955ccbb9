import { MenuItem } from '../models/menu.model';

export class Menu {
  public static pages: MenuItem[] = [
    {
      group: 'Base',
      separator: false,
      items: [
        {
          icon: 'assets/icons/heroicons/outline/chart-pie.svg',
          label: 'Dashboard',
          route: '/template/dashboard',
          children: [{ label: 'Nfts', route: '/template/dashboard/nfts' }],
        },
        {
          icon: 'assets/icons/heroicons/outline/lock-closed.svg',
          label: 'Auth',
          route: '/template/auth',
          children: [
            { label: 'Sign up', route: '/template/auth/sign-up' },
            { label: 'Sign in', route: '/template/auth/sign-in' },
            { label: 'Forgot Password', route: '/template/auth/forgot-password' },
            { label: 'New Password', route: '/template/auth/new-password' },
            { label: 'Two Steps', route: '/template/auth/two-steps' },
          ],
        },
        {
          icon: 'assets/icons/heroicons/outline/exclamation-triangle.svg',
          label: 'Errors',
          route: '/template/errors',
          children: [
            { label: '404', route: '/template/errors/404' },
            { label: '500', route: '/template/errors/500' },
          ],
        },
        {
          icon: 'assets/icons/heroicons/outline/cube.svg',
          label: 'Components',
          route: '/template/components',
          children: [{ label: 'Table', route: '/template/components/table' }],
        },
      ],
    },
  ];
}
