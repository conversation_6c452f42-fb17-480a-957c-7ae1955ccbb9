package cl.ufro.dci.propiedad.domain;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Entidad que representa los tipos de hospedajes disponibles en la plataforma.
 * Esta clase almacena información sobre las diferentes categorías de alojamientos
 * que pueden registrarse, como casa, apartamento, cabaña, hotel, etc.
 */
@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
public class tipoHospedaje {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long tipoId;

    private String nombre;
}
