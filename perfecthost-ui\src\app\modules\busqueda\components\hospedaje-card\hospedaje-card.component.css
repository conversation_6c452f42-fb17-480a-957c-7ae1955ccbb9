.carta-hospedaje {
    display: flex;
    flex-direction: column;
    border-radius: 1rem;
    background-color: var(--background);
    padding: 0.5rem;
    cursor: pointer;
    width: fit-content;
}
  
.imagen-hospedaje {
    width: 230px;
    height: 230px;
    background-size: cover;
    background-position: center;
    border-radius: 1rem;
    cursor: pointer;
    transition: opacity 0.15s ease-in-out;
}
  
.imagen-hospedaje:hover {
    opacity: 0.75;
}
  
.titulo-hospedaje {
    margin-left: 0.5rem;
    margin-top: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--foreground);
    text-overflow: ellipsis;
    white-space: nowrap;
}
  
.info-hospedaje {
    margin-left: 0.5rem;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    font-size: 0.875rem;
    color: var(--muted-foreground);
    text-overflow: ellipsis;
    white-space: nowrap;
}