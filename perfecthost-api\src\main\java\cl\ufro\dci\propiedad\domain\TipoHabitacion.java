package cl.ufro.dci.propiedad.domain;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * Entidad que representa un tipo de habitación en el sistema.
 * <p>
 * Esta clase almacena información sobre los diferentes tipos de habitaciones
 * disponibles para los hospedajes, como Individual, Doble, Matrimonial, etc.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TipoHabitacion {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String nombre;
}
