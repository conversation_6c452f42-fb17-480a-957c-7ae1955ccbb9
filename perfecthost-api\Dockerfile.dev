# Dockerfile para desarrollo con hot-reload
FROM maven:3.9.8-amazoncorretto-21

# Establecer directorio de trabajo
WORKDIR /app

# Copiar archivos de configuración de Maven
COPY pom.xml .

# Descargar dependencias
RUN mvn dependency:go-offline -B

# Copiar código fuente
COPY src ./src

# Configurar encoding
ENV JAVA_TOOL_OPTIONS -Dfile.encoding=UTF-8

# Exponer puertos
EXPOSE 8081 35729

# Comando para desarrollo con hot-reload
CMD ["mvn", "spring-boot:run", "-Dspring-boot.run.jvmArguments=-Dspring.devtools.restart.enabled=true"]
