package cl.ufro.dci.propiedad.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Objeto de Transferencia de Datos (DTO) para la creación y actualización de hospedajes.
 * Se utiliza para recibir los datos necesarios para crear o actualizar un hospedaje
 * desde la capa de presentación hacia la capa de servicio.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HospedajeCreateDTO {
    private String hosNombre;
    private int hosCapacidad;
    private int hosHabitaciones;
    private int hosBanios;
    private int hosPiscinas;
    private boolean hosWifi;
    private String politica;
    private String hosDescripcion;
    private String hosImagen;
    private float hosPrecio;
    private String hosPais;
    private String hosRegion;
    private String hosCiudad;
    private String hosProvincia;
    private String hosComuna;
    private String hosCalle;
    private int hosNumero;
    private String hosPiso;
    private String hosDpto;
    private String hosCodigoPostal;
    private String hosLatitud;
    private String hosLongitud;
    private Long hosTipoHospedajeId;
    private Long hosTipoHabitacionId;
    private Long hosTipoCocinaId;
    private Long hosComodidadesId;
    private Long usuarioId;
}
