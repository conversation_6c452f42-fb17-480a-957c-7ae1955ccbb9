stages:
  - build
  - deploy

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"

  API_IMAGE: registry.gitlab.com/dci-project/ingsoft-dci/proyecto/p1s2025/perfecthost-app/api
  UI_IMAGE: registry.gitlab.com/dci-project/ingsoft-dci/proyecto/p1s2025/perfecthost-app/ui

# Build Backend (Spring Boot API)
build-api:
  stage: build
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  before_script:
    - echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
  script:
    - cd perfecthost-api
    - docker build -t $API_IMAGE:$CI_COMMIT_SHA -t $API_IMAGE:latest .
    - docker push $API_IMAGE:$CI_COMMIT_SHA
    - docker push $API_IMAGE:latest
  only:
    - feature/cicd

# Build Frontend (Angular UI)
build-ui:
  stage: build
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  before_script:
    - echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
  script:
    - cd perfecthost-ui
    - docker build -t $UI_IMAGE:$CI_COMMIT_SHA -t $UI_IMAGE:latest .
    - docker push $UI_IMAGE:$CI_COMMIT_SHA
    - docker push $UI_IMAGE:latest
  only:
    - feature/cicd

# Deploy to VPS
deploy-vps:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache openssh-client sshpass
    - mkdir -p ~/.ssh
    - echo "$SSH_SERVER_KEY_KEY" | tr -d '\r' > ~/.ssh/id_ed25519
    - chmod 600 ~/.ssh/id_ed25519
    - ssh-keyscan -H $VPS_HOST >> ~/.ssh/known_hosts
  script:
    # Crear directorio temporal en VPS
    - ssh -i ~/.ssh/id_ed25519 $VPS_USER@$VPS_HOST "mkdir -p /tmp/perfecthost-deploy-$CI_COMMIT_SHA"

    # Copiar archivos necesarios
    - scp -i ~/.ssh/id_ed25519 docker-compose.prod.yml $VPS_USER@$VPS_HOST:/tmp/perfecthost-deploy-$CI_COMMIT_SHA/docker-compose.yml
    - scp -i ~/.ssh/id_ed25519 deploy.sh $VPS_USER@$VPS_HOST:/tmp/perfecthost-deploy-$CI_COMMIT_SHA/
    
    # Crear archivo de variables de entorno dinámicamente
    - |
      cat > .env.prod << EOF
      UI_PORT=${UI_PORT}
      API_PORT=${API_PORT}
      SPRING_PROFILE=${SPRING_PROFILE}
      DATABASE_URL=${DATABASE_URL}
      DATABASE_USERNAME=${DATABASE_USERNAME}
      DATABASE_PASSWORD=${DATABASE_PASSWORD}
      JWT_SECRET=${JWT_SECRET}
      CORS_ALLOWED_ORIGINS=${CORS_ALLOWED_ORIGINS}
      API_URL=${API_URL}
      GITLAB_USERNAME=${CI_REGISTRY_USER}
      GITLAB_TOKEN=${GITLAB_TOKEN}
      EOF
    - scp -i ~/.ssh/id_ed25519 .env.prod $VPS_USER@$VPS_HOST:/tmp/perfecthost-deploy-$CI_COMMIT_SHA/.env

    # Ejecutar despliegue
    - ssh -i ~/.ssh/id_ed25519 $VPS_USER@$VPS_HOST "cd /tmp/perfecthost-deploy-$CI_COMMIT_SHA && chmod +x deploy.sh && ./deploy.sh"

    # Limpiar archivos temporales
    - ssh -i ~/.ssh/id_ed25519 $VPS_USER@$VPS_HOST "rm -rf /tmp/perfecthost-deploy-$CI_COMMIT_SHA"
  environment:
    name: production
    url: http://$VPS_HOST:4200
  only:
    - feature/cicd
  when: manual
