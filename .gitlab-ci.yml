stages:
  - build
  - deploy

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  # Registry URLs
  API_IMAGE: $CI_REGISTRY_IMAGE/perfecthost-api
  UI_IMAGE: $CI_REGISTRY_IMAGE/perfecthost-ui

# Build Backend (Spring Boot API)
build-api:
  stage: build
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  before_script:
    - echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
  script:
    - cd perfecthost-api
    - docker build -t $API_IMAGE:$CI_COMMIT_SHA -t $API_IMAGE:latest .
    - docker push $API_IMAGE:$CI_COMMIT_SHA
    - docker push $API_IMAGE:latest
  only:
    - main
    - develop
    - merge_requests

# Build Frontend (Angular UI)
build-ui:
  stage: build
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  before_script:
    - echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
  script:
    - cd perfecthost-ui
    - docker build -t $UI_IMAGE:$CI_COMMIT_SHA -t $UI_IMAGE:latest .
    - docker push $UI_IMAGE:$CI_COMMIT_SHA
    - docker push $UI_IMAGE:latest
  only:
    - main
    - develop
    - merge_requests

# Deploy to VPS
deploy-vps:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache openssh-client sshpass
    - mkdir -p ~/.ssh
    - echo "$VPS_SSH_PRIVATE_KEY" | tr -d '\r' > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - ssh-keyscan -H $VPS_HOST >> ~/.ssh/known_hosts
  script:
    # Crear directorio temporal en VPS
    - ssh $VPS_USER@$VPS_HOST "mkdir -p /tmp/perfecthost-deploy-$CI_COMMIT_SHA"
    
    # Copiar archivos necesarios
    - scp docker-compose.prod.yml $VPS_USER@$VPS_HOST:/tmp/perfecthost-deploy-$CI_COMMIT_SHA/docker-compose.yml
    - scp deploy.sh $VPS_USER@$VPS_HOST:/tmp/perfecthost-deploy-$CI_COMMIT_SHA/
    
    # Crear archivo de variables de entorno
    - echo "$PRODUCTION_ENV" > .env.prod
    - scp .env.prod $VPS_USER@$VPS_HOST:/tmp/perfecthost-deploy-$CI_COMMIT_SHA/.env
    
    # Ejecutar despliegue
    - ssh $VPS_USER@$VPS_HOST "cd /tmp/perfecthost-deploy-$CI_COMMIT_SHA && chmod +x deploy.sh && ./deploy.sh"
    
    # Limpiar archivos temporales
    - ssh $VPS_USER@$VPS_HOST "rm -rf /tmp/perfecthost-deploy-$CI_COMMIT_SHA"
  environment:
    name: production
    url: http://$VPS_HOST:4200
  only:
    - feature/cicd
  when: manual
