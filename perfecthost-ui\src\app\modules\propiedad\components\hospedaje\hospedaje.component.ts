import {Component, EventEmitter, inject, Input, Output} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { EstadoHospedaje, Hospedaje } from 'src/app/modules/propiedad/models/hospedaje.model';
import { HospedajeService } from '../../services/hospedaje.service';
import {Observable, of} from 'rxjs';
import {RouterLink} from '@angular/router';

@Component({
  selector: 'app-hospedaje',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, RouterLink],
  templateUrl: './hospedaje.component.html',
  styleUrl: './hospedaje.component.css'
})
export class HospedajeComponent {
  @Input() hospedaje!: Hospedaje
  @Output() estadoCambiado = new EventEmitter<Hospedaje>()
  @Output() eliminado = new EventEmitter<number>()

  private hospedajeService = inject(HospedajeService)

  estadoTexto = ""
  estadoHospedaje = EstadoHospedaje

  ngOnInit() {
    this.actualizarEstadoTexto()
  }

  private actualizarEstadoTexto() {
    switch (this.hospedaje.hosEstado) {
      case EstadoHospedaje.ACTIVO:
        this.estadoTexto = "Activo"
        break
      case EstadoHospedaje.INACTIVO:
        this.estadoTexto = "Inactivo"
        break
      case EstadoHospedaje.EN_REVISION:
        this.estadoTexto = "En revisión"
        break
      case EstadoHospedaje.RECHAZADO:
        this.estadoTexto = "Rechazado"
        break
      default:
        this.estadoTexto = "Desconocido"
    }
  }

  cambiarEstado(nuevoEstado: EstadoHospedaje) {
    this.hospedajeService.cambiarEstadoHospedaje(this.hospedaje.hosId, nuevoEstado).subscribe({
      next: (hospedajeActualizado) => {
        this.hospedaje = hospedajeActualizado
        this.actualizarEstadoTexto()
        this.estadoCambiado.emit(hospedajeActualizado)
      },
      error: (error) => console.error("Error al cambiar estado:", error),
    })
  }

  eliminar() {
    if (confirm("¿Estás seguro de que deseas eliminar este hospedaje?")) {
      this.hospedajeService.eliminarHospedaje(this.hospedaje.hosId).subscribe({
        next: () => this.eliminado.emit(this.hospedaje.hosId),
        error: (error) => console.error("Error al eliminar:", error),
      })
    }
  }

  getEstadoClase(): string {
    switch (this.hospedaje.hosEstado) {
      case EstadoHospedaje.ACTIVO:
        return "estado-activo"
      case EstadoHospedaje.INACTIVO:
        return "estado-inactivo"
      case EstadoHospedaje.EN_REVISION:
        return "estado-revision"
      case EstadoHospedaje.RECHAZADO:
        return "estado-rechazado"
      default:
        return ""
    }
  }
}
