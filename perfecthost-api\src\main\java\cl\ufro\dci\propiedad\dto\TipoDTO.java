package cl.ufro.dci.propiedad.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Objeto de Transferencia de Datos (DTO) para representar tipos de hospedaje.
 * Se utiliza para transferir información de tipos de hospedaje entre la capa de servicio y la capa de presentación.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TipoDTO {
    private Long tipoId;
    private String nombre;
}
