/* Estilos específicos para el layout del template */

/* Asegurar que el layout ocupe toda la pantalla */
.h-screen {
  height: 100vh;
}

/* Flexbox para el layout principal */
.flex-1 {
  flex: 1;
}

/* Overflow para el contenido principal */
.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}

/* Transiciones suaves */
.transition-all {
  transition: all 0.3s ease-in-out;
}
