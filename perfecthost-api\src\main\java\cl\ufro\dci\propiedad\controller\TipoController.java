package cl.ufro.dci.propiedad.controller;

import cl.ufro.dci.propiedad.dto.TipoDTO;
import cl.ufro.dci.propiedad.service.TipoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Controlador REST que maneja las peticiones relacionadas con los tipos de hospedaje.
 * Proporciona endpoints para crear, leer, actualizar y eliminar tipos de hospedaje.
 */
@RestController
@RequestMapping("/api/tipos")
@CrossOrigin(origins = "http://localhost:4200")
public class TipoController {

    private final TipoService tipoService;

    @Autowired
    public TipoController(TipoService tipoService) {
        this.tipoService = tipoService;
    }

    @GetMapping
    public ResponseEntity<List<TipoDTO>> getAllTipos() {
        List<TipoDTO> tipos = tipoService.getAllTipos();
        return new ResponseEntity<>(tipos, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    public ResponseEntity<TipoDTO> getTipoById(@PathVariable Long id) {
        TipoDTO tipo = tipoService.getTipoById(id);
        return new ResponseEntity<>(tipo, HttpStatus.OK);
    }

    @PostMapping
    public ResponseEntity<TipoDTO> createTipo(@RequestBody TipoDTO tipoDTO) {
        TipoDTO createdTipo = tipoService.createTipo(tipoDTO);
        return new ResponseEntity<>(createdTipo, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    public ResponseEntity<TipoDTO> updateTipo(@PathVariable Long id, @RequestBody TipoDTO tipoDTO) {
        TipoDTO updatedTipo = tipoService.updateTipo(id, tipoDTO);
        return new ResponseEntity<>(updatedTipo, HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteTipo(@PathVariable Long id) {
        tipoService.deleteTipo(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
