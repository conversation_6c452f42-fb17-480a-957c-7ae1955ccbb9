import { Component } from '@angular/core';
import { BuscadorComponent } from '../components/buscador/buscador.component';
import { PerfecthostFooterComponent } from '../../layout/components/perfecthost-footer/perfecthost-footer.component';


@Component({
  selector: 'app-busqueda-page',
  standalone: true,
  imports: [BuscadorComponent, PerfecthostFooterComponent],
  template: `
    <header>
      <button class="ml-auto text-white bg-primary hover:bg-primary/90">Iniciar sesión</button>
    </header>
    <section class="py-9">
      <h1 class="text-center text-4xl md:text-6xl font-bold text-foreground mb-9">
        Bienvenido a <span class="text-primary">PerfectHost</span>
      </h1>
      <app-buscador />
    </section>
    <hr>
    <section class="px-5 py-5 mb-50">
      <h2 class="text-2xl font-bold"><PERSON>lt<PERSON>s alojamientos</h2>
    </section>
    <app-perfecthost-footer />
  `,
  styles: [`
    header {
        padding: 1.5rem;
        display: flex;
        align-items: center;
        width: 100%;
        top: 0;
        background-color: white;
        z-index: 1000;
    }

    button {
        padding: 0.75rem 1rem;
        border-radius: 9999px;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        cursor: pointer;
    }
  `]
})
export class BusquedaPage {}
