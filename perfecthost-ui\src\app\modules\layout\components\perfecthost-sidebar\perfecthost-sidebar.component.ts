import { Ng<PERSON><PERSON>, NgIf } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { RouterLink } from '@angular/router';
import { AngularSvgIconModule } from 'angular-svg-icon';
import { PerfectHostMenuService } from '../../services/perfecthost-menu.service';
import { PerfectHostSidebarMenuComponent } from './perfecthost-sidebar-menu/perfecthost-sidebar-menu.component';

@Component({
  selector: 'app-perfecthost-sidebar',
  templateUrl: './perfecthost-sidebar.component.html',
  styleUrls: ['./perfecthost-sidebar.component.css'],
  imports: [
    NgClass, 
    NgIf, 
    RouterLink,
    AngularSvgIconModule, 
    PerfectHostSidebarMenuComponent
  ],
})
export class PerfectHostSidebarComponent implements OnInit {

  constructor(public perfectHostMenuService: PerfectHostMenuService) {}

  ngOnInit(): void {}

  public toggleSidebar() {
    this.perfectHostMenuService.toggleSidebar();
  }
}
