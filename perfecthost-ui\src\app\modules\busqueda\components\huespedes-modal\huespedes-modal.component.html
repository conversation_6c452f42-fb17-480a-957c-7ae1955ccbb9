<div *ngIf="isOpen" class="fixed inset-0 flex items-center justify-center bg-black/40 bg-opacity-50">
    <div class="bg-white rounded-lg shadow-lg w-full max-w-md mx-4 p-6 relative" [formGroup]="form">
        <h2 class="text-xl font-semibold mb-4">{{ title }}</h2>

        <p>Adultos</p>
        <p class="text-xs text-muted-foreground">Edad: 13 años o más</p>
        <input id="cantidadAdultos" type="number" min="0" formControlName="cantidadAdultos">

        <p>Niños</p>
        <p class="text-xs text-muted-foreground">Edad: 12 años o menos</p>
        <input id="cantidadMenores" type="number" min="0" formControlName="cantidadMenores">

        <p>Mascotas</p>
        <input id="cantidadMascotas" type="number" min="0" formControlName="cantidadMascotas">

        <button
            (click)="close()"
            class="text-xl font-medium absolute top-2 right-2 text-gray-500 hover:text-gray-700 hover:cursor-pointer"
            aria-label="Cerrar"
            >
            <svg-icon src="assets/icons/heroicons/outline/x.svg" [svgClass]="'h-5 w-5'"></svg-icon>
        </button>

        <div class="mt-6 flex justify-center">
            <button
                type="button"
                (click)="close()"
                class="bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded-md font-medium transition-colors hover:cursor-pointer"
                >
                Aceptar
            </button>
        </div>
    </div>
</div>
  