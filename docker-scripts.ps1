# Scripts de Docker para PerfectHost
# Ejecutar desde la raíz del proyecto

# Función para construir y levantar los servicios
function Start-PerfectHost {
    Write-Host "🚀 Iniciando PerfectHost..." -ForegroundColor Green
    docker-compose up --build -d
    Write-Host "✅ Servicios iniciados!" -ForegroundColor Green
    Write-Host "🌐 Frontend: http://localhost" -ForegroundColor Cyan
    Write-Host "🔧 Backend: http://localhost:8080" -ForegroundColor Cyan
}

# Función para detener los servicios
function Stop-PerfectHost {
    Write-Host "🛑 Deteniendo PerfectHost..." -ForegroundColor Yellow
    docker-compose down
    Write-Host "✅ Servicios detenidos!" -ForegroundColor Green
}

# Función para ver logs
function Show-PerfectHostLogs {
    param(
        [string]$Service = ""
    )
    
    if ($Service -eq "") {
        docker-compose logs -f
    } else {
        docker-compose logs -f $Service
    }
}

# Función para reconstruir completamente
function Rebuild-PerfectHost {
    Write-Host "🔄 Reconstruyendo PerfectHost..." -ForegroundColor Blue
    docker-compose down
    docker-compose build --no-cache
    docker-compose up -d
    Write-Host "✅ Reconstrucción completa!" -ForegroundColor Green
}

# Función para limpiar contenedores e imágenes
function Clean-PerfectHost {
    Write-Host "🧹 Limpiando contenedores e imágenes..." -ForegroundColor Red
    docker-compose down --rmi all --volumes --remove-orphans
    Write-Host "✅ Limpieza completa!" -ForegroundColor Green
}

# Función para mostrar estado de los servicios
function Status-PerfectHost {
    Write-Host "📊 Estado de los servicios:" -ForegroundColor Cyan
    docker-compose ps
}

# Mostrar ayuda
function Show-Help {
    Write-Host "🐳 Scripts de Docker para PerfectHost" -ForegroundColor Magenta
    Write-Host ""
    Write-Host "Comandos disponibles:" -ForegroundColor White
    Write-Host "  Start-PerfectHost      - Construir y levantar servicios" -ForegroundColor Green
    Write-Host "  Stop-PerfectHost       - Detener servicios" -ForegroundColor Yellow
    Write-Host "  Show-PerfectHostLogs   - Ver logs (opcional: -Service api|ui)" -ForegroundColor Cyan
    Write-Host "  Rebuild-PerfectHost    - Reconstruir completamente" -ForegroundColor Blue
    Write-Host "  Clean-PerfectHost      - Limpiar todo" -ForegroundColor Red
    Write-Host "  Status-PerfectHost     - Ver estado de servicios" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Ejemplos:" -ForegroundColor White
    Write-Host "  Start-PerfectHost"
    Write-Host "  Show-PerfectHostLogs -Service perfecthost-api"
    Write-Host "  Show-PerfectHostLogs -Service perfecthost-ui"
}

# Mostrar ayuda por defecto
Show-Help
