.mis-propiedades-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header h1 {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.btn-agregar {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #10b981;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.btn-agregar:hover {
  background-color: #059669;
}

.filtros {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
}

.busqueda {
  flex: 1;
  position: relative;
}

.busqueda i {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
}

.busqueda input {
  width: 100%;
  padding: 12px 12px 12px 44px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.2s ease;
}

.busqueda input:focus {
  outline: none;
  border-color: #10b981;
}

.filtro-grupo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filtro-grupo label {
  font-size: 14px;
  color: #4b5563;
}

.filtro-grupo select {
  padding: 10px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  background-color: white;
  cursor: pointer;
}

.propiedades-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
}

.estado-mensaje {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;
  text-align: center;
}

.estado-mensaje i {
  font-size: 48px;
  color: #9ca3af;
  margin-bottom: 16px;
}

.estado-mensaje p {
  font-size: 18px;
  color: #4b5563;
  margin-bottom: 24px;
}

.estado-mensaje button {
  background-color: #10b981;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.estado-mensaje button:hover {
  background-color: #059669;
}

.estado-mensaje.error i {
  color: #ef4444;
}

.estado-mensaje.error p {
  color: #b91c1c;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(16, 185, 129, 0.1);
  border-radius: 50%;
  border-top-color: #10b981;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .filtros {
    flex-direction: column;
    align-items: stretch;
  }

  .propiedades-grid {
    grid-template-columns: 1fr;
  }
}
