import {Component, inject, OnInit} from '@angular/core';
import {FormBuilder, FormGroup, ReactiveFormsModule, Validators} from '@angular/forms';
import { HospedajeService } from '../../services/hospedaje.service';
import {ActivatedRoute, Router, RouterLink} from '@angular/router';
import {Catalogo, Hospedaje, Pais, Tipo } from '../../models/hospedaje.model';
import {finalize, forkJoin} from 'rxjs';
import {NgForOf, NgIf} from '@angular/common';

@Component({
  selector: 'app-editar-propiedad',
  imports: [
    RouterLink,
    NgIf,
    ReactiveFormsModule,
    NgForOf
  ],
  templateUrl: './editar-propiedad.component.html',
  styleUrl: './editar-propiedad.component.css'
})
export class EditarPropiedadComponent implements OnInit {
  private fb = inject(FormBuilder)
  private hospedajeService = inject(HospedajeService)
  private router = inject(Router)
  private route = inject(ActivatedRoute)

  propiedadForm!: FormGroup
  hospedajeId!: number
  hospedaje: Hospedaje | null = null

  tipos: Tipo[] = []
  tiposHabitacion: Catalogo[] = []
  tiposCocina: Catalogo[] = []
  tiposComodidad: Catalogo[] = []
  paises: Pais[] = []

  cargando = true
  cargandoCatalogos = true
  enviando = false
  error: string | null = null

  // Para la vista previa de imagen
  imagenPreview: string | null = null

  ngOnInit(): void {
    this.crearFormulario()

    // Obtener el ID del hospedaje de la URL
    this.hospedajeId = Number(this.route.snapshot.paramMap.get("id"))

    if (isNaN(this.hospedajeId)) {
      this.error = "ID de hospedaje inválido"
      this.cargando = false
      return
    }

    // Cargar datos del hospedaje y catálogos en paralelo
    this.cargarDatos()
  }

  crearFormulario(): void {
    this.propiedadForm = this.fb.group({
      // Información básica
      hosNombre: ["", [Validators.required, Validators.minLength(5), Validators.maxLength(100)]],
      hosDescripcion: ["", [Validators.required, Validators.minLength(20)]],
      hosTipoHospedajeId: [null, Validators.required],
      hosTipoHabitacionId: [null, Validators.required],

      // Capacidad y características
      hosCapacidad: [1, [Validators.required, Validators.min(1), Validators.max(20)]],
      hosHabitaciones: [1, [Validators.required, Validators.min(1), Validators.max(10)]],
      hosBanios: [1, [Validators.required, Validators.min(1), Validators.max(10)]],
      hosPiscinas: [0, [Validators.required, Validators.min(0), Validators.max(5)]],
      hosTipoCocinaId: [null, Validators.required],
      hosComodidadesId: [null, Validators.required],
      hosPrecio: [0, [Validators.required, Validators.min(1)]],
      hosWifi: [false],

      // Políticas
      politica: ["", Validators.required],

      // Imagen
      hosImagen: ["", Validators.required],

      // Ubicación
      hosPais: ["", Validators.required],
      hosRegion: ["", Validators.required],
      hosCiudad: ["", Validators.required],
      hosProvincia: [""],
      hosComuna: ["", Validators.required],
      hosCalle: ["", Validators.required],
      hosNumero: [null, [Validators.required, Validators.min(1)]],
      hosPiso: [""],
      hosDpto: [""],
      hosCodigoPostal: [""],
      hosLatitud: ["0"],
      hosLongitud: ["0"],

      // Usuario (en una aplicación real, esto vendría de un servicio de autenticación)
      usuarioId: [1],
    })

    // Suscribirse a cambios en el campo de imagen para mostrar vista previa
    this.propiedadForm.get("hosImagen")?.valueChanges.subscribe((url) => {
      if (url && url.trim() !== "") {
        this.imagenPreview = url
      } else {
        this.imagenPreview = null
      }
    })
  }

  cargarDatos(): void {
    this.cargando = true
    this.cargandoCatalogos = true
    this.error = null

    // Cargar el hospedaje y los catálogos en paralelo
    forkJoin({
      hospedaje: this.hospedajeService.getHospedajeById(this.hospedajeId),
      tipos: this.hospedajeService.getTipos(),
      tiposHabitacion: this.hospedajeService.getTiposHabitacion(),
      tiposCocina: this.hospedajeService.getTiposCocina(),
      tiposComodidad: this.hospedajeService.getTiposComodidad(),
      paises: this.hospedajeService.getPaises(),
    })
      .pipe(
        finalize(() => {
          this.cargando = false
          this.cargandoCatalogos = false
        }),
      )
      .subscribe({
        next: (result) => {
          this.hospedaje = result.hospedaje
          this.tipos = result.tipos
          this.tiposHabitacion = result.tiposHabitacion
          this.tiposCocina = result.tiposCocina
          this.tiposComodidad = result.tiposComodidad
          this.paises = result.paises

          // Llenar el formulario con los datos del hospedaje
          this.llenarFormulario()
        },
        error: (error) => {
          console.error("Error al cargar datos:", error)
          this.error = "Error al cargar los datos del hospedaje. Por favor, intente nuevamente."
        },
      })
  }

  llenarFormulario(): void {
    if (!this.hospedaje) return

    this.propiedadForm.patchValue({
      hosNombre: this.hospedaje.hosNombre,
      hosDescripcion: this.hospedaje.hosDescripcion,
      hosTipoHospedajeId: this.hospedaje.hosTipoHospedajeId,
      hosTipoHabitacionId: this.hospedaje.hosTipoHabitacionId,
      hosCapacidad: this.hospedaje.hosCapacidad,
      hosHabitaciones: this.hospedaje.hosHabitaciones,
      hosBanios: this.hospedaje.hosBanios,
      hosPiscinas: this.hospedaje.hosPiscinas,
      hosTipoCocinaId: this.hospedaje.hosTipoCocinaId,
      hosComodidadesId: this.hospedaje.hosComodidadesId,
      hosPrecio: this.hospedaje.hosPrecio,
      hosWifi: this.hospedaje.hosWifi,
      politica: this.hospedaje.politica,
      hosImagen: this.hospedaje.hosImagen,
      hosPais: this.hospedaje.hosPais,
      hosRegion: this.hospedaje.hosRegion,
      hosCiudad: this.hospedaje.hosCiudad,
      hosProvincia: this.hospedaje.hosProvincia,
      hosComuna: this.hospedaje.hosComuna,
      hosCalle: this.hospedaje.hosCalle,
      hosNumero: this.hospedaje.hosNumero,
      hosPiso: this.hospedaje.hosPiso,
      hosDpto: this.hospedaje.hosDpto,
      hosCodigoPostal: this.hospedaje.hosCodigoPostal,
      hosLatitud: this.hospedaje.hosLatitud,
      hosLongitud: this.hospedaje.hosLongitud,
      usuarioId: this.hospedaje.usuarioId,
    })

    // Actualizar la vista previa de la imagen
    this.imagenPreview = this.hospedaje.hosImagen
  }

  onSubmit(): void {
    if (this.propiedadForm.invalid) {
      // Marcar todos los campos como tocados para mostrar errores
      Object.keys(this.propiedadForm.controls).forEach((key) => {
        const control = this.propiedadForm.get(key)
        control?.markAsTouched()
      })
      return
    }

    this.enviando = true
    this.error = null

    this.hospedajeService
      .actualizarHospedaje(this.hospedajeId, this.propiedadForm.value)
      .pipe(finalize(() => (this.enviando = false)))
      .subscribe({
        next: () => {
          // Redirigir a la lista de propiedades con un mensaje de éxito
          this.router.navigate(["/propiedades"], {
            state: { mensaje: "Propiedad actualizada exitosamente" },
          })
        },
        error: (err) => {
          this.error = err.message || "Error al actualizar la propiedad. Por favor, intente nuevamente."
          window.scrollTo(0, 0) // Desplazar al inicio para mostrar el error
        },
      })
  }

  // Helpers para validación
  campoInvalido(campo: string): boolean {
    const control = this.propiedadForm.get(campo)
    return !!control && control.invalid && (control.dirty || control.touched)
  }

  obtenerErrorMensaje(campo: string): string {
    const control = this.propiedadForm.get(campo)

    if (!control) return ""

    if (control.hasError("required")) {
      return "Este campo es obligatorio"
    }

    if (control.hasError("minlength")) {
      const minLength = control.getError("minlength").requiredLength
      return `Debe tener al menos ${minLength} caracteres`
    }

    if (control.hasError("maxlength")) {
      const maxLength = control.getError("maxlength").requiredLength
      return `No puede tener más de ${maxLength} caracteres`
    }

    if (control.hasError("min")) {
      const min = control.getError("min").min
      return `El valor mínimo es ${min}`
    }

    if (control.hasError("max")) {
      const max = control.getError("max").max
      return `El valor máximo es ${max}`
    }

    return "Campo inválido"
  }
}
