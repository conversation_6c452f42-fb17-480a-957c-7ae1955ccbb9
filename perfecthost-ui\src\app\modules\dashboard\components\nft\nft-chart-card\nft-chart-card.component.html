<div class="bg-background flex-col rounded-lg px-8 py-8 sm:flex-row">
  <!-- Card Header -->
  <div class="mb-2 flex items-center justify-between">
    <div class="flex-col">
      <div class="flex items-center space-x-1">
        <span class="text-muted-foreground text-lg font-semibold">$</span>
        <span class="text-foreground text-4xl font-semibold">3,274.94</span>
        <div class="flex items-center rounded-md bg-green-500/25 py-1 pr-2 pl-1 text-xs font-semibold text-green-500">
          <svg-icon src="assets/icons/heroicons/outline/arrow-sm-up.svg" [svgClass]="'h-4 w-4'"></svg-icon>
          9.2%
        </div>
      </div>
      <span class="text-muted-foreground text-sm">Etherium rate</span>
    </div>
    <div
      class="rounded-xs bg-card text-muted-foreground hover:bg-muted hover:text-foreground cursor-pointer px-1 py-1 text-center text-xs">
      <svg-icon src="assets/icons/heroicons/outline/dots-horizontal.svg" [svgClass]="'h-3 w-3'"></svg-icon>
    </div>
  </div>
  <!-- end Card Header -->
  <div class="mb-3 grid grid-cols-5 items-center justify-between gap-2 text-center">
    <div class="bg-primary text-primary-foreground hover:bg-primary cursor-pointer rounded-md p-0.5 text-sm">1d</div>
    <div class="hover:bg-primary hover:text-primary-foreground cursor-pointer rounded-md p-0.5 text-sm text-gray-400">
      5d
    </div>
    <div class="hover:bg-primary hover:text-primary-foreground cursor-pointer rounded-md p-0.5 text-sm text-gray-400">
      1m
    </div>
    <div class="hover:bg-primary hover:text-primary-foreground cursor-pointer rounded-md p-0.5 text-sm text-gray-400">
      6m
    </div>
    <div class="hover:bg-primary hover:text-primary-foreground cursor-pointer rounded-md p-0.5 text-sm text-gray-400">
      1y
    </div>
  </div>
  <!-- Card Body -->
  <apx-chart
    [series]="chartOptions.series!"
    [chart]="chartOptions.chart!"
    [legend]="chartOptions.legend!"
    [dataLabels]="chartOptions.dataLabels!"
    [fill]="chartOptions.fill!"
    [stroke]="chartOptions.stroke!"
    [xaxis]="chartOptions.xaxis!"
    [yaxis]="chartOptions.yaxis!"
    [states]="chartOptions.states!"
    [tooltip]="chartOptions.tooltip!"
    [colors]="chartOptions.colors!"
    [grid]="chartOptions.grid!"
    [title]="chartOptions.title!">
  </apx-chart>
  <!-- end Chart -->

  <!--Table-->
  <table class="mt-2 w-full table-auto text-sm">
    <!--begin::Table head-->
    <thead>
      <tr>
        <th></th>
        <th></th>
        <th></th>
      </tr>
    </thead>
    <!--end::Table head-->
    <!--begin::Table body-->
    <tbody>
      <tr>
        <td class="py-2">
          <a class="text-muted-foreground text-sm font-semibold">2:30 PM</a>
        </td>
        <td class="py-2 text-right">
          <span class="text-foreground text-sm font-semibold">$2,756.26</span>
        </td>
        <td class="py-2 text-right">
          <span class="text-sm font-semibold text-rose-600">-139.34</span>
        </td>
      </tr>
      <tr>
        <td class="py-2">
          <a class="text-muted-foreground text-sm font-semibold">3:10 PM</a>
        </td>
        <td class="py-2 text-right">
          <span class="text-foreground text-sm font-semibold">$3,207.03</span>
        </td>
        <td class="py-2 text-right">
          <span class="text-sm font-semibold text-green-500">+576.24</span>
        </td>
      </tr>
      <tr>
        <td class="py-2">
          <a class="text-muted-foreground text-sm font-semibold">3:55 PM</a>
        </td>
        <td class="py-2 text-right">
          <span class="text-foreground text-sm font-semibold">$3,274.94</span>
        </td>
        <td class="py-2 text-right">
          <span class="text-sm font-semibold text-green-500">+124.03</span>
        </td>
      </tr>
    </tbody>
    <!--end::Table body-->
  </table>
  <!--end Table-->
</div>
