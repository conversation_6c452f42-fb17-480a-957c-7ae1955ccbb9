#!/bin/bash

# Script de despliegue para PerfectHost
# Este script se ejecuta en el VPS para desplegar la aplicación

set -e  # Salir si cualquier comando falla

echo "🚀 Iniciando despliegue de PerfectHost..."

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para logging
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# Verificar que existe el archivo .env
if [ ! -f ".env" ]; then
    error "Archivo .env no encontrado"
fi

# Cargar variables de entorno
log "Cargando variables de entorno..."
export $(cat .env | grep -v '^#' | xargs)

# Verificar variables requeridas
required_vars=("GITLAB_USERNAME" "GITLAB_TOKEN")
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        error "Variable de entorno requerida no encontrada: $var"
    fi
done

# Login a GitLab Container Registry
log "Realizando login a GitLab Container Registry..."
echo "$GITLAB_TOKEN" | docker login -u "$GITLAB_USERNAME" --password-stdin "$CI_REGISTRY" || error "Fallo en login a GitLab Registry"

# Detener contenedores existentes si existen
log "Deteniendo contenedores existentes..."
if docker compose -f docker-compose.yml ps -q | grep -q .; then
    docker compose -f docker-compose.yml down || warning "No se pudieron detener algunos contenedores"
fi

# Limpiar imágenes antiguas (opcional, para ahorrar espacio)
log "Limpiando imágenes Docker antiguas..."
docker image prune -f || warning "No se pudieron limpiar algunas imágenes"

# Descargar las últimas imágenes
log "Descargando últimas imágenes..."
docker compose -f docker-compose.yml pull || error "Fallo al descargar imágenes"

# Levantar servicios
log "Levantando servicios..."
docker compose -f docker-compose.yml up -d || error "Fallo al levantar servicios"

# Esperar a que los servicios estén listos
log "Esperando a que los servicios estén listos..."
sleep 30

# Verificar estado de los servicios
log "Verificando estado de los servicios..."
if docker compose -f docker-compose.yml ps | grep -q "Up"; then
    log "✅ Servicios desplegados exitosamente"
    
    # Mostrar información de los servicios
    info "Estado de los servicios:"
    docker compose -f docker-compose.yml ps
    
    # Mostrar URLs de acceso
    info "URLs de acceso:"
    echo -e "${BLUE}Frontend: http://$(hostname -I | awk '{print $1}'):${UI_PORT:-4200}${NC}"
    echo -e "${BLUE}Backend:  http://$(hostname -I | awk '{print $1}'):${API_PORT:-8080}${NC}"
    
else
    error "❌ Algunos servicios no se iniciaron correctamente"
fi

# Mostrar logs recientes para debugging
info "Logs recientes de los servicios:"
docker compose -f docker-compose.yml logs --tail=20

log "🎉 Despliegue completado exitosamente"
