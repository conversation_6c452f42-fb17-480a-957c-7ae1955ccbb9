import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>I<PERSON>, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { RouterLink, RouterLinkActive } from '@angular/router';
import { AngularSvgIconModule } from 'angular-svg-icon';
import { SubMenuItem } from 'src/app/core/models/menu.model';
import { PerfectHostMenuService } from '../../../services/perfecthost-menu.service';
import { PerfectHostSidebarSubmenuComponent } from '../perfecthost-sidebar-submenu/perfecthost-sidebar-submenu.component';

@Component({
  selector: 'app-perfecthost-sidebar-menu',
  templateUrl: './perfecthost-sidebar-menu.component.html',
  styleUrls: ['./perfecthost-sidebar-menu.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgFor,
    NgClass,
    AngularSvgIconModule,
    NgTemplateOutlet,
    RouterLink,
    Router<PERSON>inkActive,
    NgIf,
    PerfectHostSidebarSubmenuComponent,
  ],
})
export class PerfectHostSidebarMenuComponent implements OnInit {
  constructor(public perfectHostMenuService: PerfectHostMenuService) {}

  public toggleMenu(subMenu: SubMenuItem) {
    this.perfectHostMenuService.toggleMenu(subMenu);
  }

  ngOnInit(): void {}
}
