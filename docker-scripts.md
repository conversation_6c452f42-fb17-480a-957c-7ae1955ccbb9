# Scripts de Docker para PerfectHost

## Comandos básicos

### Levantar todos los servicios
```bash
docker-compose up -d
```

### Ver logs de todos los servicios
```bash
docker-compose logs -f
```

### Ver logs de un servicio específico
```bash
docker-compose logs -f api
docker-compose logs -f frontend
docker-compose logs -f postgres
```

### Parar todos los servicios
```bash
docker-compose down
```

### Parar y eliminar volúmenes (CUIDADO: elimina datos de la BD)
```bash
docker-compose down -v
```

### Reconstruir imágenes
```bash
docker-compose build
```

### Reconstruir y levantar
```bash
docker-compose up --build -d
```

## Acceso a los servicios

- **Frontend Angular**: http://localhost:4200
- **API Spring Boot**: http://localhost:8080
- **Base de datos PostgreSQL**: localhost:5432
  - Usuario: perfecthost
  - Contraseña: perfecthost123
  - Base de datos: perfecthost

## Comandos útiles para desarrollo

### Conectarse a la base de datos
```bash
docker exec -it perfecthost-postgres psql -U perfecthost -d perfecthost
```

### Ver contenedores en ejecución
```bash
docker ps
```

### Limpiar imágenes no utilizadas
```bash
docker system prune -f
```
