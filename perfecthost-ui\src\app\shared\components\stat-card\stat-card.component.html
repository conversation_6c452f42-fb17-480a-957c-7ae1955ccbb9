<div class="bg-card rounded-lg border border-border p-6">
  <div class="flex items-center justify-between">
    <div>
      <p class="text-sm text-muted-foreground">{{ label }}</p>
      <p class="text-2xl font-bold text-foreground">{{ value }}</p>
      <p *ngIf="subtitle" class="text-xs text-muted-foreground mt-1">{{ subtitle }}</p>
    </div>
    <div 
      class="w-12 h-12 rounded-lg flex items-center justify-center"
      [ngClass]="iconBackgroundClass">
      <svg 
        class="w-6 h-6" 
        [ngClass]="iconColorClass"
        fill="none" 
        stroke="currentColor" 
        viewBox="0 0 24 24">
        <path 
          stroke-linecap="round" 
          stroke-linejoin="round" 
          stroke-width="2" 
          [attr.d]="iconPath">
        </path>
      </svg>
    </div>
  </div>
</div>
