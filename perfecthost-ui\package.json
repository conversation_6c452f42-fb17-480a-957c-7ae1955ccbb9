{"name": "angular-tailwind", "displayName": "Angular Tailwind", "version": "0.10.1", "description": "Angular & Tailwind CSS Admin Dashboard Starter Kit, Free and Open Source", "homepage": "https://github.com/lannodev/angular-tailwind#readme", "repository": {"type": "git", "url": "git+https://github.com/lannodev/angular-tailwind.git"}, "keywords": ["angular", "tailwind", "starter-kit", "tailwind-template", "dark mode", "open source"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "scripts": {"ng": "ng", "start": "ng serve --open", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "test:e2e": "npx playwright test --ui", "prettier": "prettier --config ./.prettierrc --write \"src/{app,environments}/**/*{.ts,.html,.scss,.json}\"", "prettier:verify": "prettier --config ./.prettierrc --check \"src/{app,environments}/**/*{.ts,.html,.scss,.json}\"", "prettier:staged": "pretty-quick --staged", "lint": "ng lint"}, "private": true, "dependencies": {"@angular/animations": "20.1.0", "@angular/cdk": "20.1.0", "@angular/common": "20.1.0", "@angular/compiler": "20.1.0", "@angular/core": "20.1.0", "@angular/forms": "20.1.0", "@angular/material": "20.1.0", "@angular/platform-browser": "20.1.0", "@angular/platform-browser-dynamic": "20.1.0", "@angular/router": "20.1.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/leaflet": "^1.9.17", "angular-svg-icon": "19.1.1", "apexcharts": "5.2.0", "autoprefixer": "10.4.21", "leaflet": "^1.9.4", "ng-apexcharts": "1.16.0", "ngx-sonner": "3.1.0", "postcss": "8.5.6", "rxjs": "7.8.2", "tslib": "^2.3.0", "zone.js": "0.15.1"}, "devDependencies": {"@angular-devkit/build-angular": "20.1.0", "@angular/cli": "20.1.0", "@angular/compiler-cli": "20.1.0", "@types/jasmine": "^5.1.8", "@types/node": "24.0.12", "autoprefixer": "^10.4.7", "jasmine": "^5.8.0", "postcss": "^8.5.1", "prettier": "3.6.2", "prettier-plugin-tailwindcss": "0.6.14", "tailwind-scrollbar": "4.0.2", "tailwindcss": "4.1.11", "typescript": "5.8.3"}}