import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';

import { Hospedaje } from 'src/app/modules/propiedad/models/hospedaje.model';

import { HospedajeCardComponent } from '../hospedaje-card/hospedaje-card.component';

@Component({
  selector: 'app-lista-hospedajes',
  imports: [CommonModule, HospedajeCardComponent],
  templateUrl: './lista-hospedajes.component.html',
  styleUrl: './lista-hospedajes.component.css'
})
export class ListaHospedajesComponent {
  @Input() hospedajes: Hospedaje[] = [];
  @Output() hospedajeSelected = new EventEmitter<Hospedaje>();

  selectedHospedajeId: number | null = null;

  selectHospedaje(hospedaje: Hospedaje): void {
    this.hospedajeSelected.emit(hospedaje);
  }
}
