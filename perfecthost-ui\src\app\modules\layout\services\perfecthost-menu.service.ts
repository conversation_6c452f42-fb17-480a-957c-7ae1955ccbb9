import { Injectable, signal } from '@angular/core';
import { Router } from '@angular/router';
import { MenuItem, SubMenuItem } from 'src/app/core/models/menu.model';
import { PerfectHostMenu } from 'src/app/core/constants/perfecthost-menu';

@Injectable({
  providedIn: 'root',
})
export class PerfectHostMenuService {
  private _showSidebar = signal(true);
  private _showMobileMenu = signal(false);
  private _pagesMenu = signal<MenuItem[]>([]);

  constructor(private router: Router) {
    this._pagesMenu.set(PerfectHostMenu.pages);
  }

  get showSideBar() {
    return this._showSidebar();
  }

  get showMobileMenu() {
    return this._showMobileMenu();
  }

  get pagesMenu() {
    return this._pagesMenu();
  }

  set showSideBar(value: boolean) {
    this._showSidebar.set(value);
  }
  
  set showMobileMenu(value: boolean) {
    this._showMobileMenu.set(value);
  }

  public toggleSidebar() {
    this._showSidebar.set(!this._showSidebar());
  }

  public toggleMenu(menu: any) {
    this.showSideBar = true;
    menu.expanded = !menu.expanded;
  }

  public toggleSubMenu(submenu: SubMenuItem) {
    submenu.expanded = !submenu.expanded;
  }

  public closeMenu() {
    this.showMobileMenu = false;
  }

  public isActive(route: string): boolean {
    return this.router.url === route;
  }
}
