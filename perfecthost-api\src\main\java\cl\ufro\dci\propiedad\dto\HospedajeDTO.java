package cl.ufro.dci.propiedad.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Objeto de Transferencia de Datos (DTO) para representar información completa de un hospedaje.
 * Se utiliza para transferir información detallada de hospedajes entre la capa de servicio y la capa de presentación.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HospedajeDTO {
    private Long hosId;
    private String hosNombre;
    private int hosCapacidad;
    private int hosHabitaciones;
    private int hosBanios;
    private int hosPiscinas;
    private boolean hosWifi;
    private String politica;
    private String hosDescripcion;
    private String hosImagen;
    private int hosEstado;
    private float hosPrecio;
    private String hosPais;
    private String hosRegion;
    private String hosCiudad;
    private String hosProvincia;
    private String hosComuna;
    private String hosCalle;
    private int hosNumero;
    private String hosPiso;
    private String hosDpto;
    private String hosCodigoPostal;
    private String hosLatitud;
    private String hosLongitud;
    private String hosDireccionCompleta;
    private String hosFechaCreacion;
    private String hosFechaModificacion;
    private Long hosTipoHospedajeId;
    private String hosTipoHospedajeNombre;
    private Long hosTipoHabitacionId;
    private String hosTipoHabitacionNombre;
    private Long hosTipoCocinaId;
    private String hosTipoCocianNombre;
    private Long hosComodidadesId;
    private String hosComodidadesNombre;
    private Long usuarioId;
}
