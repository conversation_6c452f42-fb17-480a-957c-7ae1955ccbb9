services:
  # Servicio del backend (Spring Boot)
  perfecthost-api:
    image: registry.gitlab.com/dci-project/ingsoft-dci/proyecto/p1s2025/perfecthost-app/api:latest
    container_name: perfecthost-api
    ports:
      - "${API_PORT:-8080}:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=${SPRING_PROFILE:-docker}
      - SERVER_PORT=8080
      - DATABASE_URL=${DATABASE_URL}
      - DATABASE_USERNAME=${DATABASE_USERNAME}
      - DATABASE_PASSWORD=${DATABASE_PASSWORD}
      - JWT_SECRET=${JWT_SECRET}
      - CORS_ALLOWED_ORIGINS=${CORS_ALLOWED_ORIGINS}
    networks:
      - perfecthost-network
    restart: unless-stopped

  # Servicio del frontend (Angular + nginx)
  perfecthost-ui:
    image: registry.gitlab.com/dci-project/ingsoft-dci/proyecto/p1s2025/perfecthost-app/ui:latest
    container_name: perfecthost-ui
    ports:
      - "${UI_PORT:-4200}:80"
    environment:
      - API_URL=${API_URL:-http://localhost:8080}
    depends_on:
      - perfecthost-api
    networks:
      - perfecthost-network
    restart: unless-stopped

networks:
  perfecthost-network:
    driver: bridge

volumes:
  # Volúmenes para persistencia si es necesario
  api-data:
    driver: local
