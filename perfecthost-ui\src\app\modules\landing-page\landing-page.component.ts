import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AngularSvgIconModule } from 'angular-svg-icon';
import { PerfecthostNavbarComponent } from '../layout/components/perfecthost-navbar/perfecthost-navbar.component';
import { PerfecthostFooterComponent } from '../layout/components/perfecthost-footer/perfecthost-footer.component';
import { PropiedadCardComponent } from '../propiedad/components/propiedad-card/propiedad-card.component';

@Component({
  selector: 'app-landing-page',
  templateUrl: './landing-page.component.html',
  styleUrls: ['./landing-page.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    AngularSvgIconModule,
    PerfecthostNavbarComponent,
    PerfecthostFooterComponent,
    PropiedadCardComponent
  ],
})
export class LandingPageComponent implements OnInit {
  today = new Date().toISOString().split('T')[0];
  
  propiedades = [
    {
      id: '1',
      imagen: 'assets/images/img-01.jpg',
      nombre: 'Casa de Playa Moderna',
      ubicacion: 'Pucón, Chile',
      precio: 150000,
      calificacion: 4.9,
      fechas: '1-15 Jun',
      esFavorito: false
    },
    {
      id: '2',
      imagen: 'assets/images/img-02.jpg',
      nombre: 'Cabaña en el Bosque',
      ubicacion: 'Villarrica, Chile',
      precio: 120000,
      calificacion: 4.8,
      fechas: '5-12 Jun',
      esFavorito: true
    },
    {
      id: '3',
      imagen: 'assets/images/img-03.jpg',
      nombre: 'Apartamento Céntrico',
      ubicacion: 'Temuco, Chile',
      precio: 85000,
      calificacion: 4.7,
      fechas: '10-20 Jun',
      esFavorito: false
    },
    {
      id: '4',
      imagen: 'assets/images/img-01.jpg',
      nombre: 'Casa con Vista al Lago',
      ubicacion: 'Puerto Varas, Chile',
      precio: 200000,
      calificacion: 5.0,
      fechas: '15-25 Jun',
      esFavorito: false
    }
  ];

  constructor() {}

  ngOnInit(): void {
    // Aquí podrías cargar las propiedades desde un servicio
  }
}
