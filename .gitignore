### Spring / Backend ###
HELP.md
perfecthost-api/target/
!.mvn/wrapper/*
!**/src/main/**/target/
!**/src/test/**/target/

### Maven Wrapper ###
perfecthost-api/mvnw
perfecthost-api/mvnw.cmd
perfecthost-api/.mvn/wrapper/maven-wrapper.jar
perfecthost-api/.mvn/wrapper/maven-wrapper.properties

### STS ###
perfecthost-api/.apt_generated
perfecthost-api/.classpath
perfecthost-api/.factorypath
perfecthost-api/.project
perfecthost-api/.settings
perfecthost-api/.springBeans
perfecthost-api/.sts4-cache

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/
!**/src/main/**/build/
!**/src/test/**/build/

### VS Code ###
.vscode/

### Angular / Frontend ###
# Compiled output
perfecthost-ui/dist
perfecthost-ui/tmp
perfecthost-ui/out-tsc
perfecthost-ui/bazel-out
perfecthost-ui/.angular
# Node
perfecthost-ui/node_modules
perfecthost-ui-legacy/node_modules
npm-debug.log
yarn-error.log

# IDEs and editors
*.sublime-workspace
.c9/
*.launch

# Miscellaneous
perfecthost-ui/.angular/cache
.sass-cache/
/connect.lock
/coverage
/libpeerconnection.log
testem.log
/typings

# System files
.DS_Store
Thumbs.db 