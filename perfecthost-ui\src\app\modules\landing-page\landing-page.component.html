<div class="min-h-screen bg-background">
  <!-- Navbar -->
  <app-perfecthost-navbar></app-perfecthost-navbar>
  <!-- Main Content -->
  <main class="container mx-auto px-4 py-8">
    <!-- Hero Section with Search Bar -->
    <section class="text-center py-16">
      <h1 class="text-4xl md:text-6xl font-bold text-foreground mb-6">
        Bienvenido a <span class="text-primary">PerfectHost</span>
      </h1>
      <!-- Search Bar -->
      <div class="max-w-4xl mx-auto mb-12">
        <div class="flex items-center gap-4 p-2 rounded-full border border-input bg-card shadow-lg hover:border-primary/50 transition-colors">
          <div class="flex-1 flex items-center gap-2 px-4">
            <svg-icon src="assets/icons/heroicons/outline/map-pin.svg" [svgClass]="'h-5 w-5 text-muted-foreground'"></svg-icon>
            <input 
              type="text" 
              placeholder="¿A dónde vas?" 
              class="w-full bg-transparent outline-none text-foreground placeholder:text-muted-foreground"
              aria-label="Destino">
          </div>
          <div class="h-8 w-px bg-border"></div>
          <div class="flex-1 px-4">
            <input 
              type="date" 
              class="w-full bg-transparent outline-none text-foreground placeholder:text-muted-foreground" 
              [min]="today"
              aria-label="Fecha de llegada">
          </div>
          <div class="h-8 w-px bg-border"></div>
          <div class="flex-1 px-4">
            <select 
              class="w-full bg-transparent outline-none text-foreground"
              aria-label="Número de huéspedes">
              <option class="bg-card text-foreground">2 huéspedes</option>
              <option class="bg-card text-foreground">3 huéspedes</option>
              <option class="bg-card text-foreground">4 huéspedes</option>
              <option class="bg-card text-foreground">5+ huéspedes</option>
            </select>
          </div>
          <button 
            type="button" 
            class="bg-primary text-primary-foreground p-3 rounded-full hover:bg-primary/90 transition-colors"
            aria-label="Buscar alojamientos">
            <svg-icon src="assets/icons/heroicons/outline/magnifying-glass.svg" [attr.svgclass]="'h-5 w-5'"></svg-icon>
          </button>
        </div>
      </div>
    </section>

    <!-- Properties Grid -->
    <section class="py-16">
      <div class="flex justify-between items-center mb-8">
        <h2 class="text-2xl font-bold text-foreground">Alojamientos destacados</h2>
        <button 
          type="button"
          class="text-primary hover:text-primary/80"
          aria-label="Ver todos los alojamientos">
          Ver todos
        </button>
      </div>
      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        <app-propiedad-card 
          *ngFor="let propiedad of propiedades" 
          [propiedad]="propiedad">
        </app-propiedad-card>
      </div>
    </section>
  </main>

  <!-- Footer -->
  <app-perfecthost-footer></app-perfecthost-footer>
</div>
