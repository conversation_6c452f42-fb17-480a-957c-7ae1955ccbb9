import { Component } from '@angular/core';
import { RecompensaComponent } from '../components/recompensa/recompensa.component';
import { PuntoComponent } from '../components/punto/punto.component';
import { AccionRecompensaComponent } from '../components/accion-recompensa/accion-recompensa.component';

@Component({
  selector: 'app-recompensas-page',
  standalone: true,
  imports: [RecompensaComponent, PuntoComponent, AccionRecompensaComponent],
  template: `
    <div class="bg-background min-h-screen">
      <div class="container mx-auto px-6 py-8">
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-foreground mb-2">Sistema de Recompensas</h1>
          <p class="text-muted-foreground">Gana puntos y obtén beneficios exclusivos</p>
        </div>
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div class="bg-card rounded-lg border border-border p-6">
            <h2 class="text-lg font-semibold text-foreground mb-4">Recompensas</h2>
            <app-recompensa></app-recompensa>
          </div>
          <div class="bg-card rounded-lg border border-border p-6">
            <h2 class="text-lg font-semibold text-foreground mb-4">Mis Puntos</h2>
            <app-punto></app-punto>
          </div>
          <div class="bg-card rounded-lg border border-border p-6">
            <h2 class="text-lg font-semibold text-foreground mb-4">Acciones</h2>
            <app-accion-recompensa></app-accion-recompensa>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .container {
      max-width: 1400px;
    }
  `]
})
export class RecompensasPage {}
