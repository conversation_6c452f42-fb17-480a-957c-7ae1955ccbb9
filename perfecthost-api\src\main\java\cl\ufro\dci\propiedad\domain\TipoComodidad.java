package cl.ufro.dci.propiedad.domain;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Entidad que representa los niveles de comodidad disponibles para los hospedajes.
 * Esta clase almacena información sobre las diferentes categorías de comodidades
 * que pueden tener los alojamientos, como básicas, estándar, premium, lujo, etc.
 */
@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TipoComodidad {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String nombre;
}
