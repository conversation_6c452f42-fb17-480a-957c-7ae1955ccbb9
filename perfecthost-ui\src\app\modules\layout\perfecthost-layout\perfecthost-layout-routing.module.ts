import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PerfectHostLayoutComponent } from './perfecthost-layout.component';

const routes: Routes = [
  {
    path: 'busqueda',
    component: PerfectHostLayoutComponent,
    children: [
      {
        path: '',
        loadComponent: () => import('../../busqueda/pages/busqueda.page').then((m) => m.BusquedaPage),
      },
    ],
  },
  {
    path: 'propiedad',
    component: PerfectHostLayoutComponent,
    children: [
      {
        path: '',
        loadComponent: () => import('../../propiedad/pages/propiedad.page').then((m) => m.PropiedadPage),
      },
    ],
  },
  {
    path: 'mensajeria',
    component: PerfectHostLayoutComponent,
    children: [
      {
        path: '',
        loadComponent: () => import('../../mensajeria/pages/mensajeria.page').then((m) => m.MensajeriaPage),
      },
    ],
  },
  {
    path: 'pagos',
    component: PerfectHostLayoutComponent,
    children: [
      {
        path: '',
        loadComponent: () => import('../../pagos/pages/pagos.page').then((m) => m.PagosPage),
      },
    ],
  },
  {
    path: 'recompensas',
    component: PerfectHostLayoutComponent,
    children: [
      {
        path: '',
        loadComponent: () => import('../../recompensas/pages/recompensas.page').then((m) => m.RecompensasPage),
      },
    ],
  },

  {
    path: 'reservacion',
    component: PerfectHostLayoutComponent,
    children: [
      {
        path: '',
        loadComponent: () => import('../../reservacion/pages/reservacion.page').then((m) => m.ReservacionPage),
      },
    ],
  },
  {
    path: 'evaluacion',
    component: PerfectHostLayoutComponent,
    children: [
      {
        path: '',
        loadComponent: () => import('../../evaluacion/pages/evaluacion.page').then((m) => m.EvaluacionPage),
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class PerfectHostLayoutRoutingModule {}
