# Dockerfile para desarrollo con hot-reload
FROM node:18-alpine

# Establecer directorio de trabajo
WORKDIR /app

# Copiar archivos de configuración de npm
COPY package*.json ./

# Instalar dependencias (incluyendo devDependencies)
RUN npm ci

# Copiar código fuente
COPY . .

# Exponer puerto para desarrollo
EXPOSE 4200

# Comando para desarrollo con hot-reload
CMD ["npm", "start", "--", "--host", "0.0.0.0", "--poll", "2000"]
