<div class="bg-card rounded-lg border border-border p-6">
  <!-- Header -->
  <div class="flex items-center justify-between mb-6" *ngIf="title || hasActions">
    <h2 class="text-lg font-semibold text-foreground">{{ title }}</h2>
    <div class="flex items-center space-x-2" *ngIf="hasActions">
      <ng-content select="[slot=actions]"></ng-content>
    </div>
  </div>

  <!-- Content -->
  <div class="content">
    <ng-content></ng-content>
  </div>
</div>
