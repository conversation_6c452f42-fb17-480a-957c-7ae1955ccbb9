<div class="bg-background relative">
  <div class="mx-auto px-5">
    <div class="flex items-center justify-between py-3.5 md:justify-start">
      <!-- Mobile Navigation Menu Button-->
      <div class="sm:order-1 md:hidden">
        <button
          (click)="toggleMobileMenu()"
          type="button"
          class="bg-muted text-muted-foreground focus:ring-primary hover:bg-muted-foreground hover:text-muted inline-flex items-center justify-center rounded-md p-2 focus:outline-hidden focus:ring-2 focus:ring-inset"
          aria-expanded="false">
          <span class="sr-only">Open menu</span>
          <!-- Heroicon name: outline/menu -->
          <svg-icon src="assets/icons/heroicons/outline/menu.svg" [svgClass]="'h-6 w-6'"> </svg-icon>
        </button>
      </div>

      <!-- Logo -->
      <div class="flex items-center justify-start gap-3 sm:order-2 ltr:md:mr-10 rtl:md:ml-10 lg:hidden">
        <a class="bg-primary flex items-center justify-center rounded-sm p-2 focus:outline-hidden focus:ring-1">
          <svg-icon src="assets/icons/logo.svg"></svg-icon>
        </a>
        <b class="text-foreground hidden text-sm font-bold sm:block"> Angular Tailwind </b>
      </div>

      <!-- Desktop Menu -->
      <div class="hidden space-x-10 sm:order-3 md:flex">
        <app-navbar-menu></app-navbar-menu>
      </div>

      <!-- Profile menu -->
      <div class="items-center justify-end sm:order-4 md:flex md:flex-1 lg:w-0">
        <!-- PerfectHost Button -->
        <a
          routerLink="/busqueda"
          class="hidden lg:flex items-center space-x-2 text-sm text-muted-foreground hover:text-primary transition-colors border border-border rounded-md px-3 py-1.5 mr-4">
          <svg-icon src="assets/icons/heroicons/outline/home.svg" [svgClass]="'h-4 w-4'"></svg-icon>
          <span>PerfectHost</span>
        </a>

        <app-profile-menu></app-profile-menu>
      </div>
    </div>
  </div>
  <!-- Mobile menu -->
  <app-navbar-mobile></app-navbar-mobile>
</div>
