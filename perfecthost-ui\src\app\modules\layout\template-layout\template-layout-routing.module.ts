import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { TemplateLayoutComponent } from './template-layout.component';

const routes: Routes = [
  {
    path: 'dashboard',
    component: TemplateLayoutComponent,
    loadChildren: () => import('../../dashboard/dashboard.module').then((m) => m.DashboardModule),
  },
  {
    path: 'components',
    component: TemplateLayoutComponent,
    loadChildren: () => import('../../uikit/uikit.module').then((m) => m.UikitModule),
  },
  {
    path: 'auth',
    loadChildren: () => import('../../auth/auth.module').then((m) => m.AuthModule),
  },
  {
    path: 'errors',
    loadChildren: () => import('../../error/error.module').then((m) => m.ErrorModule),
  },
  {
    path: 'profile',
    component: TemplateLayoutComponent,
    children: [
      {
        path: '',
        loadComponent: () => import('../../dashboard/pages/nft/nft.component').then((m) => m.NftComponent),
      },
    ],
  },
  {
    path: 'settings',
    component: TemplateLayoutComponent,
    children: [
      {
        path: '',
        loadComponent: () => import('../../dashboard/pages/nft/nft.component').then((m) => m.NftComponent),
      },
    ],
  },
  { path: '', redirectTo: 'dashboard', pathMatch: 'full' },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class TemplateLayoutRoutingModule {}
