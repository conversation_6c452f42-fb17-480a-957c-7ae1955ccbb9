<nav class="bg-background border-b border-border">
  <div class="flex items-center justify-between h-16 px-4">

    <!-- Mobile Menu <PERSON> (solo visible en móvil) -->
    <button
      type="button"
      (click)="toggleMobileMenu()"
      class="lg:hidden text-muted-foreground hover:text-foreground p-2"
      aria-label="Toggle mobile menu">
      <svg-icon src="assets/icons/heroicons/outline/menu.svg" [svgClass]="'h-6 w-6'"></svg-icon>
    </button>

    <!-- Spacer for desktop -->
    <div class="flex-1"></div>

    <!-- Right Side Actions -->
    <div class="flex items-center space-x-4">
      <!-- PerfectHost Button -->
      <a
        routerLink="/busqueda"
        class="hidden lg:flex items-center space-x-2 text-sm text-muted-foreground hover:text-primary transition-colors border border-border rounded-md px-3 py-1.5">
        <svg-icon src="assets/icons/heroicons/outline/home.svg" [class]="'h-4 w-4'"></svg-icon>
        <span>PerfectHost</span>
      </a>

      <!-- Template Button -->
      <a
        routerLink="/template"
        class="hidden lg:flex items-center space-x-2 text-sm text-muted-foreground hover:text-primary transition-colors border border-border rounded-md px-3 py-1.5">
        <svg-icon src="assets/icons/heroicons/outline/cube.svg" [class]="'h-4 w-4'"></svg-icon>
        <span>Template</span>
      </a>

      <!-- Auth Buttons -->
      <div class="hidden md:flex items-center space-x-3">
        <a
          routerLink="/auth/registro"
          class="bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded-md font-medium transition-colors">
          Registrarse
        </a>
      </div>
    </div>
  </div>
</nav>
