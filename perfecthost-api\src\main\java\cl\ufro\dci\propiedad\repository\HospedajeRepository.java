package cl.ufro.dci.propiedad.repository;

import cl.ufro.dci.propiedad.domain.Hospedaje;
import cl.ufro.dci.registro.domain.Usuario;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repositorio para la entidad Hospedaje.
 * Proporciona métodos para realizar operaciones CRUD y consultas personalizadas
 * sobre los hospedajes registrados en la plataforma.
 */
@Repository
public interface HospedajeRepository extends JpaRepository<Hospedaje, Long> {
    List<Hospedaje> findByUsuario(Usuario usuario);
    List<Hospedaje> findByHosPais(String pais);
    List<Hospedaje> findByHosCiudad(String ciudad);

    /**
     * Busca todos los hospedajes con un estado específico.
     *
     * @param estado El código de estado a filtrar (1: activo, 2: inactivo, etc.)
     * @return Lista de hospedajes con el estado especificado
     */
    List<Hospedaje> findByHosEstado(int estado);
}
