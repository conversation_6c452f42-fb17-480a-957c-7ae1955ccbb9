/**
 * Controlador REST para manejar las operaciones de búsqueda de hospedajes.
 * <p>
 * Este controlador expone endpoints para verificar el estado del servicio y
 * para obtener información sobre hospedajes disponibles.
 * </p>
 *
 * <AUTHOR>
 * <AUTHOR>
 * @version 1.0
 * @since 2025
 * @see BusquedaService
 */
package cl.ufro.dci.busqueda.controller;

import cl.ufro.dci.busqueda.service.BusquedaService;
import cl.ufro.dci.propiedad.dto.HospedajeDTO;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.CrossOrigin;
import java.util.Map;
import java.util.HashMap;
import java.util.List;
import cl.ufro.dci.busqueda.dto.BusquedaDTO;

@CrossOrigin(origins = "http://localhost:4200")
@RestController
@RequestMapping("/api/busqueda")
public class BusquedaController {
    // Controlador básico de búsqueda
    private final BusquedaService busquedaService;

    /**
     * Constructor para la inyección de dependencias del servicio de búsqueda.
     *
     * @param busquedaService Servicio de búsqueda a inyectar
     */
    
     public BusquedaController(BusquedaService busquedaService) {
        this.busquedaService = busquedaService;
    }
    
    /**
     * Endpoint para verificar el estado del servicio.
     *
     * @return ResponseEntity con un Map que contiene el estado del servicio y su nombre
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> health() {
        Map<String, String> status = new HashMap<>();
        status.put("status", "UP");
        status.put("service", "Busqueda Service");
        return ResponseEntity.ok(status);
    }

    @GetMapping("/hospedajes")
    public ResponseEntity<List<HospedajeDTO>> obtenerHospedajes(BusquedaDTO filtros) {
        System.out.println(filtros);
        List<HospedajeDTO> hospedajes = busquedaService.obtenerListaDeHospedajes(filtros.getUbicacion());
        return ResponseEntity.ok(hospedajes);
    }
}