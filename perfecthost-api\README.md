# PerfectHost API

Este es un esqueleto de API REST desarrollado con Spring Boot, listo para comenzar el desarrollo. El proyecto incluye todas las dependencias necesarias.

## Características Principales

### Dependencias Incluidas

- **Spring Boot DevTools**: Herramientas de desarrollo para reinicio automático y configuración de propiedades
- **Spring Web**: Framework para construir aplicaciones web RESTful
- **Spring Data JPA**: Persistencia de datos con Java Persistence API
- **PostgreSQL**: Driver para la base de datos PostgreSQL
- **H2 Database**: Base de datos en memoria para desarrollo
- **Spring Security**: Framework de seguridad para autenticación y autorización
- **Lombok**: Reducción de código boilerplate
- **Spring Mail**: Envío de correos electrónicos

### Estructura del Proyecto

- Paquetes organizados por dominio:
  - `registro`: Gestión de usuarios y autenticación
  - `propiedad`: Gestión de propiedades
  - `reservacion`: Gestión de reservas
  - `pagos`: Gestión de pagos y transacciones
  - `evaluacion`: Sistema de calificaciones y reseñas
  - `mensajeria`: Sistema de mensajería entre usuarios
  - `busqueda`: Funcionalidades de búsqueda y filtrado
  - `recompensas`: Sistema de recompensas y fidelización
  - `shared`: Componentes compartidos entre dominios
- Controladores en el paquete `controller`
- Servicios en el paquete `service`
- Entidades en el paquete `domain`
- Repositorios en el paquete `repository`
- DTOs en el paquete `dto`


## Inicio del Proyecto

1. **Inicialización con GitFlow**:
   ```bash
   git flow init
   ```

2. **Casos de Uso de Ramas**:
   - **Features** (nuevas funcionalidades):
     ```bash
     git flow feature start registro-usuario
     git flow feature start sistema-pagos
     git flow feature start busqueda-propiedades
     ```
   - **Bugfix** (corrección de errores):
     ```bash
     git flow bugfix start correccion-autenticacion
     git flow bugfix start fix-pago-duplicado
     ```
   - **Hotfix** (correcciones urgentes en producción):
     ```bash
     git flow hotfix start fix-seguridad-critica
     git flow hotfix start fix-caida-servicio
     ```
   - **Release** (preparación para producción):
     ```bash
     git flow release start v1.0.0
     ```

3. **Configuración del Entorno**:
   - Java 21
   - Maven
   - PostgreSQL (para producción)
   - H2 (para desarrollo)

## Configuración de Seguridad en Desarrollo

En el entorno de desarrollo, la seguridad está configurada de la siguiente manera:

### Ubicación de Configuraciones
- La configuración principal de seguridad se encuentra en: `src/main/java/cl/ufro/dci/registro/configuration/SecurityConfig.java`
- Para modificar la configuración de seguridad:
  1. Abrir el archivo `SecurityConfig.java`
  2. Modificar el método `developmentSecurityFilterChain` para desarrollo
  3. Modificar el método `productionSecurityFilterChain` para producción

### Endpoints Públicos (sin autenticación)
- Todos los endpoints de health: `/api/*/health`
- Endpoints base de cada módulo:
  - Reservación: `/api/reserva/**`
  - Pago: `/api/pago/**`
  - Búsqueda: `/api/busqueda/**`
  - Hospedaje: `/api/hospedaje/**`
  - Evaluación: `/api/evaluacion/**`
  - Mensaje: `/api/mensaje/**`
  - Recompensa: `/api/recompensa/**`
  - Notificación: `/api/notificacion/**`

### Endpoints que Requieren Autenticación
- Módulo de Usuario: `/api/usuario/**`
- Cualquier otro endpoint no especificado

### Login
- La página de login está disponible en `/login` (página por defecto de Spring Security)
- Después de iniciar sesión exitosamente, se redirige a `/api/usuario/health`

### Configuración de CSRF
- CSRF está activado por defecto para todas las peticiones
- CSRF está desactivado para:
  - Todos los endpoints de health
  - Todos los endpoints base de los módulos permitidos sin autenticación
- CSRF permanece activo para el módulo de usuario (que requiere autenticación)

### Notas para Desarrollo
- El módulo de usuario debe implementar la autenticación completa
- Una vez que la autenticación esté lista, se implementará en los demás módulos
- Los demás módulos pueden trabajar en sus funcionalidades base sin preocuparse por la autenticación o CSRF por ahora

## Contribución

1. Crear una rama feature con GitFlow
2. Implementar cambios
3. Crear pull request
4. Esperar revisión y aprobación
5. Merge a develop

## Endpoints de Health

Cada servicio expone un endpoint de health para monitorear su estado:

- **Reservación**: `GET /api/reserva/health`
  - Devuelve: `{"status": "UP", "service": "Reservacion Service"}`

- **Pago**: `GET /api/pago/health`
  - Devuelve: `{"status": "UP", "service": "Pagos Service"}`

- **Usuario**: `GET /api/usuario/health`
  - Devuelve: `{"status": "UP", "service": "Usuario Service"}`

- **Búsqueda**: `GET /api/busqueda/health`
  - Devuelve: `{"status": "UP", "service": "Busqueda Service"}`

- **Hospedaje**: `GET /api/hospedaje/health`
  - Devuelve: `{"status": "UP", "service": "Hospedaje Service"}`

- **Evaluación**: `GET /api/evaluacion/health`
  - Devuelve: `{"status": "UP", "service": "Evaluacion Service"}`

- **Mensaje**: `GET /api/mensaje/health`
  - Devuelve: `{"status": "UP", "service": "Mensajeria Service"}`

- **Recompensa**: `GET /api/recompensa/health`
  - Devuelve: `{"status": "UP", "service": "Recompensas Service"}`

- **Notificación**: `GET /api/notificacion/health`
  - Devuelve: `{"status": "UP", "service": "Notificacion Service"}`

## Recomendaciones

### Implementación de CSRF en el Módulo de Registro

El módulo de registro debe implementar correctamente el manejo de CSRF ya que es el único que tiene CSRF activado. Esto servirá como base para la implementación en los demás módulos:

1. **Obtención del Token CSRF**:
   - El token se obtiene automáticamente en la sesión después de iniciar sesión
   - Se puede obtener mediante una petición GET a cualquier endpoint
   - El token se envía en el header `X-CSRF-TOKEN` o como parámetro `_csrf`

2. **Implementación en el Cliente**:
   - Almacenar el token CSRF después de iniciar sesión
   - Incluir el token en todas las peticiones POST, PUT, DELETE
   - Renovar el token cuando expire

3. **Propagación a Otros Módulos**:
   - Una vez que el módulo de registro tenga implementado correctamente el manejo de CSRF se activará CSRF en los demás módulos uno por uno
   - Cada módulo deberá seguir el mismo patrón de implementación

4. **Consideraciones de Seguridad**:
   - No exponer el token CSRF en logs o respuestas
   - Validar el token en cada petición
   - Manejar la expiración del token adecuadamente

### Uso de Lombok

- Inicialmente usamos `@Data` para agilizar el desarrollo, pero esto importa todos los getters, setters, toString, equals y hashCode
- **IMPORTANTE**: Una vez que el desarrollo esté más avanzado y se tenga claro qué métodos se utilizan realmente, se debe optimizar usando anotaciones específicas:
  ```java
  @Getter @Setter // Solo getters y setters
  @ToString // Solo toString
  @EqualsAndHashCode // Solo equals y hashCode
  ```
- Esta optimización mejorará el rendimiento y la claridad del código

### Seguridad

- Implementar autenticación JWT
- Usar roles y permisos específicos
- Validar todas las entradas de usuario
- Implementar CORS correctamente

### Base de Datos

- Usar H2 para desarrollo local
- Configurar PostgreSQL para producción
- Implementar migraciones con Flyway o Liquibase
- Usar índices apropiados

### Desarrollo

- Seguir principios SOLID
- Usar DTOs para transferencia de datos
- Implementar manejo de errores

## Configuración

El archivo `application.properties` o `application.yml` debe contener las configuraciones necesarias para:
- Conexión a base de datos
- Configuración de seguridad
- Configuración de correo
- Configuración de CORS
- Configuración de logging

