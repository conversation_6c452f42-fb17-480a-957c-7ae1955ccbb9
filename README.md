# perfecthost-app

Repositorio monolítico para el proyecto **PerfectHost**.

## Estructura del repositorio

```
perfecthost-app/
├── perfecthost-api/      # Backend: API REST con Spring Boot
├── perfecthost-ui/       # Frontend: Aplicación Angular
├── README.md             # Este archivo
```

## Subproyectos

### 1. perfecthost-api (Backend)
- **Framework:** Spring Boot
- **Lenguaje:** Java 21
- **Gestor de dependencias:** Maven
- **Base de datos:** PostgreSQL (producción), H2 (desarrollo)
- **Documentación y estructura:** ver [`perfecthost-api/README.md`](./perfecthost-api/README.md)

### 2. perfecthost-ui (Frontend)
- **Framework:** Angular
- **Lenguaje:** TypeScript
- **Arquitectura:** Componentes Standalone (sin NgModules)
- **Gestor de dependencias:** npm
- **Documentación y estructura:** ver [`perfecthost-ui/README.md`](./perfecthost-ui/README.md)

## Versiones utilizadas

- **Java:** 21
- **Spring Boot:** (ver versión exacta en `perfecthost-api/pom.xml`)
- **Angular:** 19 (ver versión exacta en `perfecthost-ui/package.json`)
- **Node.js:** 18+ (recomendado para Angular 19)

## ¿Qué contiene este repositorio?

- **Backend (API):**
  - Gestión de usuarios, propiedades, reservas, pagos, evaluaciones, mensajería, recompensas, etc.
  - Seguridad, autenticación, manejo de errores, DTOs, servicios, repositorios y controladores.
- **Frontend (UI):**
  - Interfaz de usuario modular, organizada por dominios funcionales.
  - Componentes standalone para cada funcionalidad principal.
  - Página de prueba en la vista principal para verificar y ejemplificar el uso de los componentes.

## Cómo empezar

1. **Clona el repositorio:**
   ```bash
   git clone https://gitlab.com/dci-project/ingsoft-dci/proyecto/p1s2025/perfecthost-app.git
   cd perfecthost-app
   ```
2. **Revisa los README de cada subproyecto:**
   - [Backend: perfecthost-api/README.md](./perfecthost-api/README.md)
   - [Frontend: perfecthost-ui/README.md](./perfecthost-ui/README.md)

3. **Sigue las instrucciones de cada subproyecto para levantar el backend y el frontend.**

## Contribución

- Usa ramas feature para nuevas funcionalidades.
- Haz pull requests para revisión de código.
- Sigue la estructura y convenciones de cada subproyecto.

## Git Flow

Este proyecto utiliza Git Flow como estrategia de ramificación. A continuación se detalla el flujo de trabajo:

### Ramas principales

- `main`: Rama principal de producción
- `develop`: Rama de desarrollo donde se integran las features

### Ramas de soporte

- `feature/*`: Para desarrollo de nuevas funcionalidades
- `release/*`: Para preparación de releases
- `hotfix/*`: Para correcciones urgentes en producción
- `bugfix/*`: Para correcciones de bugs en desarrollo

### Comandos básicos de Git Flow

```bash
# Inicializar Git Flow en el repositorio
git flow init

# Iniciar una nueva feature
git flow feature start NOMBRE_FEATURE

# Finalizar una feature
git flow feature finish NOMBRE_FEATURE

# Iniciar un release
git flow release start VERSION

# Finalizar un release
git flow release finish VERSION

# Iniciar un hotfix
git flow hotfix start VERSION

# Finalizar un hotfix
git flow hotfix finish VERSION
