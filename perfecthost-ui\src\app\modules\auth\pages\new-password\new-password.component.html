<form class="my-10 space-y-6">
  <div class="text-center">
    <h2 class="text-foreground mb-1 text-3xl font-semibold">Setup New Password</h2>
    <p class="text-muted-foreground text-sm">
      Have you already reset the password ? <a class="text-primary" routerLink="/auth/sign-in"> Sign in</a>
    </p>
  </div>

  <div class="space-y-3 text-left">
    <div class="relative">
      <input type="password" id="password" class="peer block" placeholder=" " />
      <label
        for="password"
        class="bg-background text-muted-foreground peer-focus:text-primary absolute top-2 z-10 origin-[0] -translate-y-4 scale-95 transform px-2 text-sm duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-95 peer-focus:px-2 ltr:left-1 rtl:right-1">
        Password
      </label>
      <span class="text-muted-foreground absolute top-2 cursor-pointer ltr:right-3 rtl:left-3">
        <svg-icon src="assets/icons/heroicons/outline/eye-off.svg" [svgClass]="'h-5 w-5'"> </svg-icon>
        <svg-icon src="assets/icons/heroicons/outline/eye.svg" [svgClass]="'h-5 w-5 hidden'"> </svg-icon>
      </span>
    </div>
    <!-- Password Meeter -->
    <div class="grid grid-cols-4 gap-2">
      <div class="rounded-xs bg-muted h-1"></div>
      <div class="rounded-xs bg-muted h-1"></div>
      <div class="rounded-xs bg-muted h-1"></div>
      <div class="rounded-xs bg-muted h-1"></div>
    </div>
    <span class="text-muted-foreground text-xs">
      Use 8 or more characters with a mix of letters, numbers & symbols.
    </span>
    <div class="relative">
      <input type="password" id="confirm-password" class="peer block" placeholder=" " />
      <label
        for="confirm-password"
        class="bg-background text-muted-foreground peer-focus:text-primary absolute top-2 z-10 origin-[0] -translate-y-4 scale-95 transform px-2 text-sm duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-95 peer-focus:px-2 ltr:left-1 rtl:right-1">
        Confirm Password
      </label>
    </div>
  </div>

  <!-- Submit Button -->
  <div>
    <app-button routerLink="/dashboard" full impact="bold" tone="primary" shape="rounded" size="medium">
      Submit
    </app-button>
  </div>
</form>
