import { AfterViewInit, Component, inject, Input, OnChanges, SimpleChanges, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { Hospedaje } from '../../../propiedad/models/hospedaje.model';
@Component({
  selector: 'app-mapa',
  templateUrl: './mapa.component.html',
  styleUrls: ['./mapa.component.css']
})
export class MapaComponent implements AfterViewInit, OnChanges {
  private mapa: L.Map | null = null;
  private zoom: number = 13;
  private grupoMarcadores: L.LayerGroup | null = null;
  private vistaInicializada: boolean = false;
  private platformId = inject(PLATFORM_ID);

  @Input() hospedajes: Hospedaje[] = [];

  ngAfterViewInit(): void {
    this.vistaInicializada = true;
    this.actualizarVista();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['hospedajes'] && this.vistaInicializada) {
      this.actualizarVista();
    }
  }

  private actualizarVista(): void {
    if (!isPlatformBrowser(this.platformId)) return;

    if (!this.hospedajes.length) {
      this.limpiarMapa();
      return;
    }

    import('leaflet').then((L) => {
      if (!this.mapa) {
        this.inicializarMapa(L);
      }
      this.actualizarMarcadores(L);
      this.ajustarVistaMapa(L);
    });
  }

  private inicializarMapa(L: typeof import('leaflet')): void {
    const coordenadas = this.obtenerCoordenadasCentrales(L);
    this.mapa = L.map('map').setView(coordenadas, this.zoom);
    L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(this.mapa);
    this.grupoMarcadores = L.layerGroup().addTo(this.mapa);
  }

  private actualizarMarcadores(L: typeof import('leaflet')): void {
    if (!this.mapa || !this.grupoMarcadores) return;

    this.grupoMarcadores.clearLayers();

    this.obtenerCoordenadasValidas(L).forEach((coords, indice) => {
      L.marker(coords)
        .addTo(this.grupoMarcadores!)
        .bindPopup(this.crearContenidoPopup(this.hospedajes[indice]));
    });
  }

  private crearContenidoPopup(hospedaje: Hospedaje): string {
    return `
      <div class="popup-hospedaje">
        <h3>${hospedaje.hosNombre}</h3>
        <p>Precio: $${hospedaje.hosPrecio}</p>
        <p>Capacidad: ${hospedaje.hosCapacidad} personas</p>
      </div>
    `;
  }
  
  private ajustarVistaMapa(L: typeof import('leaflet')): void {
    if (!this.mapa) return;
    const bounds = L.latLngBounds(this.obtenerCoordenadasValidas(L));
    this.mapa.fitBounds(bounds, { padding: [100, 100], maxZoom: 13 });
  }

  private obtenerCoordenadasCentrales(L: typeof import('leaflet')): [number, number] {
    const coordenadasHospedajes = this.obtenerCoordenadasValidas(L);

    if (coordenadasHospedajes.length === 0) return [0, 0];

    const avgLat = coordenadasHospedajes.reduce((sum, coords) => sum + coords.lat, 0) / coordenadasHospedajes.length;
    const avgLon = coordenadasHospedajes.reduce((sum, coords) => sum + coords.lng, 0) / coordenadasHospedajes.length;
    return [avgLat, avgLon];
  }

  private obtenerCoordenadasValidas(L: typeof import('leaflet')): L.LatLng[] {
    return this.hospedajes
      .map((h): L.LatLng | null => {
        const lat = typeof h.hosLatitud === 'number' ? h.hosLatitud : parseFloat(String(h.hosLatitud));
        const lon = typeof h.hosLongitud === 'number' ? h.hosLongitud : parseFloat(String(h.hosLongitud));
        return !isNaN(lat) && !isNaN(lon) ? L.latLng(lat, lon) : null;
      })
      .filter((coords): coords is L.LatLng => coords !== null);
  }

  private limpiarMapa(): void {
    if (this.mapa) {
      this.mapa.remove();
      this.mapa = null;
      this.grupoMarcadores = null;
    }
  }
}