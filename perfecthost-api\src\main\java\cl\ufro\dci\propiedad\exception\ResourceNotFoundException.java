package cl.ufro.dci.propiedad.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Excepción que se lanza cuando un recurso solicitado no se encuentra en el sistema.
 * Esta excepción está anotada con @ResponseStatus para que Spring la traduzca automáticamente
 * a una respuesta HTTP con código de estado 404 (Not Found).
 */
@ResponseStatus(HttpStatus.NOT_FOUND)
public class ResourceNotFoundException extends RuntimeException {

    /**
     * Constructor que inicializa la excepción con un mensaje descriptivo.
     *
     * @param message Mensaje que describe la excepción
     */
    public ResourceNotFoundException(String message) {
        super(message);
    }
}
