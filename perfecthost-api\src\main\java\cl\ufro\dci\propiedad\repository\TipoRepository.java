package cl.ufro.dci.propiedad.repository;

import cl.ufro.dci.propiedad.domain.tipoHospedaje;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Repositorio para la entidad tipoHospedaje.
 * Proporciona métodos para realizar operaciones CRUD sobre los tipos de hospedajes.
 */
@Repository
public interface TipoRepository extends JpaRepository<tipoHospedaje, Long> {

    /**
     * Busca un tipo de hospedaje por su nombre.
     *
     * @param nombre El nombre del tipo de hospedaje a buscar
     * @return Un Optional que contiene el tipo de hospedaje si se encuentra, o vacío si no existe
     */
    Optional<tipoHospedaje> findByNombre(String nombre);
}
