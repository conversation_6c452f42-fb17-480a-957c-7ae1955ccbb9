import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AngularSvgIconModule } from 'angular-svg-icon';

@Component({
  selector: 'app-propiedad-card',
  templateUrl: './propiedad-card.component.html',
  standalone: true,
  imports: [CommonModule, AngularSvgIconModule]
})
export class PropiedadCardComponent {
  @Input() propiedad!: {
    id: string;
    imagen: string;
    nombre: string;
    ubicacion: string;
    precio: number;
    calificacion: number;
    fechas: string;
    esFavorito?: boolean;
  };
}
