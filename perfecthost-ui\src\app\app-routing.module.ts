import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: '',
    loadComponent: () => import('./modules/busqueda/pages/busqueda.page').then((m) => m.BusquedaPage),
    pathMatch: 'full'
  },
  {
    path: 'template',
    loadChildren: () => import('./modules/layout/template-layout/template-layout.module').then((m) => m.TemplateLayoutModule),
  },
  {
    path: 'busqueda',
    loadComponent: () => import('./modules/busqueda/pages/busqueda.page').then((m) => m.BusquedaPage),
    pathMatch: 'full'
  },
  {
    path: 'resultados',
    loadComponent: () => import('./modules/busqueda/pages/resultados.page').then((m) => m.ResultadosPage),
    pathMatch: 'full'
  },
  {
    path: 'errors',
    loadChildren: () => import('./modules/error/error.module').then((m) => m.ErrorModule),
  },
  { path: '**', redirectTo: 'errors/404' },
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
})
export class AppRoutingModule {}
