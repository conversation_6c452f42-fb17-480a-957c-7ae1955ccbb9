import { Component, OnInit } from '@angular/core';
import { Event, NavigationEnd, Router, RouterOutlet } from '@angular/router';
import { PerfecthostNavbarComponent } from '../components/perfecthost-navbar/perfecthost-navbar.component';
import { PerfectHostSidebarComponent } from '../components/perfecthost-sidebar/perfecthost-sidebar.component';

@Component({
  selector: 'app-perfecthost-layout',
  templateUrl: './perfecthost-layout.component.html',
  styleUrls: ['./perfecthost-layout.component.css'],
  imports: [RouterOutlet, PerfecthostNavbarComponent, PerfectHostSidebarComponent],
})
export class PerfectHostLayoutComponent implements OnInit {
  private mainContent: HTMLElement | null = null;

  constructor(private router: Router) {
    this.router.events.subscribe((event: Event) => {
      if (event instanceof NavigationEnd) {
        if (this.mainContent) {
          this.mainContent.scrollTop = 0;
        }
      }
    });
  }

  ngOnInit(): void {
    this.mainContent = document.getElementById('perfecthost-main-content');
  }
}
